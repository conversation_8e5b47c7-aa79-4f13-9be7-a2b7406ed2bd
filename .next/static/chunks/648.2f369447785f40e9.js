"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[648],{13648:(e,t,n)=>{n.r(t),n.d(t,{default:()=>u});var o=n(95155),s=n(12115),r=n(58892);let u=()=>{let[e,t]=(0,s.useState)({x:0,y:0}),[n,u]=(0,s.useState)(!1),[d,a]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{let e=e=>{t({x:e.clientX,y:e.clientY})},n=()=>{u(!0),setTimeout(()=>u(!1),300)},o=()=>{document.body.style.cursor="none"},s=()=>{document.body.style.cursor="auto"},r=e=>{let t=e.target;a(!!("button"===t.tagName.toLowerCase()||"a"===t.tagName.toLowerCase()||t.closest("button")||t.closest("a")))};return document.addEventListener("mousemove",e),document.addEventListener("mousedown",n),document.addEventListener("mouseenter",o),document.addEventListener("mouseleave",s),document.addEventListener("mouseover",r),()=>{document.removeEventListener("mousemove",e),document.removeEventListener("mousedown",n),document.removeEventListener("mouseenter",o),document.removeEventListener("mouseleave",s),document.removeEventListener("mouseover",r),document.body.style.cursor="auto"}},[]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(r.P.div,{className:"fixed top-0 left-0 w-8 h-8 rounded-full border border-neon-green z-[9999] pointer-events-none",animate:{x:e.x-16,y:e.y-16,scale:n?.8:d?1.5:1},transition:{type:"spring",stiffness:300,damping:20,mass:.5}}),(0,o.jsx)(r.P.div,{className:"fixed top-0 left-0 w-2 h-2 bg-neon-green rounded-full z-[10000] pointer-events-none",animate:{x:e.x-4,y:e.y-4,opacity:n?.5:1},transition:{type:"spring",stiffness:400,damping:15}})]})}}}]);