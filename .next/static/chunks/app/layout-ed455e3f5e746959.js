(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{13279:(e,t,a)=>{"use strict";a.d(t,{default:()=>o});var n=a(12115);let o=()=>((0,n.useEffect)(()=>{let e=window.location.origin,t=(e,t)=>{let a=document.getElementById(e);a&&a.remove();let n=document.createElement("script");n.id=e,n.type="application/ld+json",n.textContent=JSON.stringify(t),document.head.appendChild(n)};return t("person-schema",{"@context":"https://schema.org","@type":"Person",name:"GreenHacker",jobTitle:"Full-Stack Developer & AI Specialist",description:"Experienced full-stack developer specializing in modern web technologies, AI integration, and innovative software solutions. Proficient in React, Next.js, TypeScript, Python, and machine learning.",url:e,sameAs:["https://github.com/GreenHacker420","https://linkedin.com/in/harsh-hirawat-b657061b7","https://codeforces.com/profile/GreenHacker","https://leetcode.com/u/greenhacker420/"],knowsAbout:["JavaScript","TypeScript","React","Next.js","Python","Machine Learning","Artificial Intelligence","Full-Stack Development","Web Development","Software Engineering","Node.js","Three.js","GSAP","Tailwind CSS"],alumniOf:{"@type":"EducationalOrganization",name:"Computer Science Education"}}),t("website-schema",{"@context":"https://schema.org","@type":"WebSite",name:"GreenHacker Portfolio",description:"Professional portfolio showcasing full-stack development projects, AI integrations, and modern web technologies.",url:e,author:{"@type":"Person",name:"GreenHacker"},potentialAction:{"@type":"SearchAction",target:{"@type":"EntryPoint",urlTemplate:"".concat(e,"/?q={search_term_string}")},"query-input":"required name=search_term_string"}}),t("organization-schema",{"@context":"https://schema.org","@type":"Organization",name:"GreenHacker Development",description:"Professional software development services specializing in full-stack web applications and AI-powered solutions.",url:e,logo:"".concat(e,"/logo.jpg"),founder:{"@type":"Person",name:"GreenHacker"},contactPoint:{"@type":"ContactPoint",email:"<EMAIL>",contactType:"Professional Inquiries"}}),()=>{["person-schema","website-schema","organization-schema"].forEach(e=>{let t=document.getElementById(e);t&&t.remove()})}},[]),null)},14540:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,78346,23)),Promise.resolve().then(a.t.bind(a,30347,23)),Promise.resolve().then(a.bind(a,51999)),Promise.resolve().then(a.bind(a,25138)),Promise.resolve().then(a.bind(a,61116)),Promise.resolve().then(a.bind(a,13279))},25138:(e,t,a)=>{"use strict";a.d(t,{Yi:()=>s,default:()=>d,sx:()=>l});var n=a(95155),o=a(12115),r=a(63554),i=a(49509);let s={resumeView:{action:"view_resume",category:"resume",label:"pdf_preview"},resumeDownload:{action:"download_resume",category:"resume",label:"pdf_download"},resumeError:{action:"resume_error",category:"resume",label:"pdf_load_failed"},contactFormSubmit:{action:"submit_form",category:"contact",label:"contact_form"},projectView:{action:"view_project",category:"projects",label:"project_details"},projectLink:{action:"click_project_link",category:"projects",label:"external_link"},githubStatsView:{action:"view_github_stats",category:"github",label:"stats_display"},aiChatStart:{action:"start_chat",category:"ai_chat",label:"terminal_chat"},aiChatMessage:{action:"send_message",category:"ai_chat",label:"user_message"},threeDInteraction:{action:"interact_3d",category:"3d_elements",label:"canvas_interaction"},sectionView:{action:"view_section",category:"navigation",label:"scroll_to_section"}},l=e=>{window.gtag&&window.gtag("event",e.action,{event_category:e.category,event_label:e.label,value:e.value,custom_parameter_timestamp:new Date().toISOString()})},d=e=>{let{measurementId:t=i.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}=e;return((0,o.useEffect)(()=>{if(!t)return void console.warn("Google Analytics measurement ID not provided");window.gtag=window.gtag||function(){window.gtag.q=window.gtag.q||[],window.gtag.q.push(arguments)},window.gtag("js",new Date),window.gtag("config",t,{page_title:"GreenHacker Portfolio",page_location:window.location.href,send_page_view:!0,enhanced_measurement:{scrolls:!0,outbound_clicks:!0,site_search:!0,video_engagement:!0,file_downloads:!0},custom_map:{custom_parameter_1:"section_name",custom_parameter_2:"interaction_type"}}),l({action:"page_view",category:"engagement",label:"initial_load"})},[t]),t)?(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(r.default,{src:"https://www.googletagmanager.com/gtag/js?id=".concat(t),strategy:"afterInteractive"})}):null}},30347:()=>{},51999:(e,t,a)=>{"use strict";a.d(t,{Providers:()=>C});var n=a(95155),o=a(72922),r=a(26715),i=a(12115),s=a(61764),l=a(59434);let d=s.Kq;s.bL,s.l9,i.forwardRef((e,t)=>{let{className:a,sideOffset:o=4,...r}=e;return(0,n.jsx)(s.UC,{ref:t,sideOffset:o,className:(0,l.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...r})}).displayName=s.UC.displayName;let c=(0,i.createContext)(void 0),u=e=>{let{children:t}=e,[a,o]=(0,i.useState)("dark");return(0,i.useEffect)(()=>{let e=localStorage.getItem("theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches;e?(o(e),document.documentElement.classList.toggle("dark","dark"===e)):t&&(o("dark"),document.documentElement.classList.add("dark"))},[]),(0,n.jsx)(c.Provider,{value:{theme:a,toggleTheme:()=>{let e="dark"===a?"light":"dark";o(e),document.documentElement.classList.toggle("dark","dark"===e),localStorage.setItem("theme",e)}},children:t})};var m=a(87481),p=a(26621),g=a(74466),h=a(54416);let f=p.Kq,v=i.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,n.jsx)(p.LM,{ref:t,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",a),...o})});v.displayName=p.LM.displayName;let x=(0,g.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),w=i.forwardRef((e,t)=>{let{className:a,variant:o,...r}=e;return(0,n.jsx)(p.bL,{ref:t,className:(0,l.cn)(x({variant:o}),a),...r})});w.displayName=p.bL.displayName,i.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,n.jsx)(p.rc,{ref:t,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",a),...o})}).displayName=p.rc.displayName;let b=i.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,n.jsx)(p.bm,{ref:t,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",a),"toast-close":"",...o,children:(0,n.jsx)(h.A,{className:"h-4 w-4"})})});b.displayName=p.bm.displayName;let y=i.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,n.jsx)(p.hE,{ref:t,className:(0,l.cn)("text-sm font-semibold",a),...o})});y.displayName=p.hE.displayName;let _=i.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,n.jsx)(p.VY,{ref:t,className:(0,l.cn)("text-sm opacity-90",a),...o})});function j(){let{toasts:e}=(0,m.dj)();return(0,n.jsxs)(f,{children:[e.map(function(e){let{id:t,title:a,description:o,action:r,...i}=e;return(0,n.jsxs)(w,{...i,children:[(0,n.jsxs)("div",{className:"grid gap-1",children:[a&&(0,n.jsx)(y,{children:a}),o&&(0,n.jsx)(_,{children:o})]}),r,(0,n.jsx)(b,{})]},t)}),(0,n.jsx)(v,{})]})}_.displayName=p.VY.displayName;var E=a(51362),N=a(56671);let S=e=>{let{...t}=e,{theme:a="system"}=(0,E.D)();return(0,n.jsx)(N.l$,{theme:a,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...t})};var k=a(58892);let T=()=>{let[e,t]=(0,i.useState)(0),[a,o]=(0,i.useState)("Initializing system..."),[r,s]=(0,i.useState)(!0),[l,d]=(0,i.useState)(!1),c=[{text:"Initializing system...",duration:1200},{text:"Establishing secure connection...",duration:1e3},{text:"Authenticating credentials...",duration:800},{text:"Bypassing security protocols...",duration:1500},{text:"Loading developer assets...",duration:1e3},{text:"Compiling portfolio data...",duration:1200},{text:"Optimizing display modules...",duration:900},{text:"Rendering interface...",duration:1300},{text:"System ready. Welcome to GreenHacker portfolio v2.0",duration:1e3}];return(0,i.useEffect)(()=>{let e=setInterval(()=>{s(e=>!e)},500),a=0,n=setTimeout(function e(){if(a<c.length){let{text:n,duration:r}=c[a];o(n),t(Math.min(100,Math.round((a+1)/c.length*100))),a++,setTimeout(e,r)}else d(!0),setTimeout(()=>{{let e=new Event("loadingComplete");window.dispatchEvent(e)}},1e3)},500);return()=>{clearInterval(e),clearTimeout(n)}},[]),(0,n.jsxs)(k.P.div,{className:"fixed inset-0 bg-black flex items-center justify-center z-50",initial:{opacity:1},exit:{opacity:0},transition:{duration:.6,ease:"easeInOut"},children:[(0,n.jsxs)(k.P.div,{className:"w-full max-w-3xl bg-black border border-neon-green p-6 rounded-md shadow-neon-green terminal-window",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.8}}},initial:"hidden",animate:"visible",children:[(0,n.jsxs)("div",{className:"terminal-header flex items-center justify-between mb-4",children:[(0,n.jsx)("div",{className:"text-neon-green font-mono text-sm",children:"~/green-hacker/portfolio"}),(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500"}),(0,n.jsx)("div",{className:"w-3 h-3 rounded-full bg-yellow-500"}),(0,n.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"})]})]}),(0,n.jsxs)("div",{className:"terminal-content space-y-2 font-mono text-sm overflow-hidden",children:[(0,n.jsxs)("div",{className:"line",children:[(0,n.jsx)("span",{className:"text-neon-blue",children:"$ "}),(0,n.jsx)("span",{className:"text-white",children:"load portfolio --env=production --secure"})]}),(0,n.jsxs)(k.P.div,{className:"line text-neon-green",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:[a,r?"▋":" "]}),(0,n.jsxs)(k.P.div,{className:"line",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:[(0,n.jsxs)("div",{className:"text-github-text",children:["Progress: ",e,"%"]}),(0,n.jsx)("div",{className:"w-full bg-github-dark rounded-full h-2 mt-1",children:(0,n.jsx)(k.P.div,{className:"h-2 rounded-full bg-neon-green",initial:{width:0},animate:{width:"".concat(e,"%")},transition:{duration:.5}})})]}),l&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(k.P.div,{className:"line",initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:[(0,n.jsx)("span",{className:"text-neon-blue",children:"$ "}),(0,n.jsx)("span",{className:"text-white",children:"launch --mode=interactive"})]}),(0,n.jsx)(k.P.div,{className:"line text-neon-purple",initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},children:"Launching portfolio interface..."})]})]}),(0,n.jsx)("div",{className:"ascii-art mt-8 text-neon-green font-mono text-xs whitespace-pre",children:" ██████╗ ██████╗ ███████╗███████╗███╗   ██╗██╗  ██╗ █████╗  ██████╗██╗  ██╗███████╗██████╗\n██╔════╝ ██╔══██╗██╔════╝██╔════╝████╗  ██║██║  ██║██╔══██╗██╔════╝██║ ██╔╝██╔════╝██╔══██╗\n██║  ███╗██████╔╝█████╗  █████╗  ██╔██╗ ██║███████║███████║██║     █████╔╝ █████╗  ██████╔╝\n██║   ██║██╔══██╗██╔══╝  ██╔══╝  ██║╚██╗██║██╔══██║██╔══██║██║     ██╔═██╗ ██╔══╝  ██╔══██╗\n╚██████╔╝██║  ██║███████╗███████╗██║ ╚████║██║  ██║██║  ██║╚██████╗██║  ██╗███████╗██║  ██║\n ╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝"}),l&&(0,n.jsxs)(k.P.div,{className:"mt-6 text-center",initial:{opacity:0},animate:{opacity:1},transition:{delay:.8},children:[(0,n.jsx)("span",{className:"text-github-text text-sm",children:"Press "}),(0,n.jsx)("span",{className:"px-2 py-1 bg-github-light rounded text-white text-sm mx-1",children:"ENTER"}),(0,n.jsx)("span",{className:"text-github-text text-sm",children:" to continue"})]})]}),(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n        .terminal-window {\n          box-shadow: 0 0 10px rgba(63, 185, 80, 0.3), 0 0 20px rgba(63, 185, 80, 0.2);\n        }\n\n        @keyframes scan {\n          from { top: 0; }\n          to { top: 100%; }\n        }\n\n        .terminal-window::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          height: 3px;\n          background-color: rgba(63, 185, 80, 0.5);\n          animation: scan 3s linear infinite;\n        }\n      "}})]})};var P=a(55028);let I=(0,P.default)(()=>a.e(648).then(a.bind(a,13648)),{loadableGenerated:{webpack:()=>[13648]},ssr:!1}),A=(0,P.default)(()=>a.e(436).then(a.bind(a,74436)),{loadableGenerated:{webpack:()=>[74436]},ssr:!1}),L=(0,P.default)(()=>Promise.all([a.e(840),a.e(892)]).then(a.bind(a,34840)),{loadableGenerated:{webpack:()=>[34840]},ssr:!1}),O=new o.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1}}});function C(e){let{children:t}=e,[a,o]=(0,i.useState)(!0),[s,l]=(0,i.useState)(!1),[c,m]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{m(!0);let e=()=>{l(window.innerWidth<768)};if(e(),window.addEventListener("resize",e),sessionStorage.getItem("loadingShown"))o(!1);else{let t=()=>{setTimeout(()=>{o(!1),sessionStorage.setItem("loadingShown","true")},1e3)};window.addEventListener("loadingComplete",t);let a=setTimeout(()=>{o(!1),sessionStorage.setItem("loadingShown","true")},12e3);return()=>{window.removeEventListener("loadingComplete",t),window.removeEventListener("resize",e),clearTimeout(a)}}return()=>{window.removeEventListener("resize",e)}},[]),c)?(0,n.jsx)(u,{children:(0,n.jsx)(r.Ht,{client:O,children:(0,n.jsxs)(d,{children:[(0,n.jsx)(j,{}),(0,n.jsx)(S,{}),a&&(0,n.jsx)(T,{}),(0,n.jsx)(A,{}),!s&&(0,n.jsx)(I,{}),t,(0,n.jsx)(L,{})]})})}):null}},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>r});var n=a(52596),o=a(39688);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,o.QP)((0,n.$)(t))}},61116:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});var n=a(12115),o=a(25138);let r=()=>((0,n.useEffect)(()=>{let e=e=>{(0,o.sx)({action:"web_vitals",category:"performance",label:e.name,value:Math.round(e.value)}),window.gtag&&window.gtag("event",e.name,{event_category:"Web Vitals",event_label:e.id,value:Math.round(e.value),custom_parameter_rating:e.rating,custom_parameter_delta:Math.round(e.delta),non_interaction:!0})};a.e(63).then(a.bind(a,77063)).then(t=>{t.onCLS&&t.onCLS(e),t.onFCP&&t.onFCP(e),t.onLCP&&t.onLCP(e),t.onTTFB&&t.onTTFB(e),t.onINP&&t.onINP(e)}).catch(e=>{console.warn("Failed to load web-vitals:",e)});let t=new PerformanceObserver(e=>{for(let t of e.getEntries()){if("longtask"===t.entryType&&(0,o.sx)({action:"long_task",category:"performance",label:"main_thread_blocking",value:Math.round(t.duration)}),"navigation"===t.entryType){let e=t.domContentLoadedEventEnd-t.domContentLoadedEventStart;(0,o.sx)({action:"dom_content_loaded",category:"performance",label:"page_load",value:Math.round(e)});let a=t.loadEventEnd-t.loadEventStart;(0,o.sx)({action:"load_complete",category:"performance",label:"page_load",value:Math.round(a)})}"resource"===t.entryType&&t.duration>1e3&&(0,o.sx)({action:"slow_resource",category:"performance",label:t.initiatorType||"unknown",value:Math.round(t.duration)})}});try{t.observe({entryTypes:["longtask","navigation","resource"]})}catch(e){console.warn("Performance observer not supported:",e)}let n=()=>{"hidden"===document.visibilityState?(0,o.sx)({action:"page_hidden",category:"engagement",label:"visibility_change"}):(0,o.sx)({action:"page_visible",category:"engagement",label:"visibility_change"})};document.addEventListener("visibilitychange",n);let r=Date.now(),i=!0,s=()=>{if(i){let e=Date.now()-r;e>1e4&&(0,o.sx)({action:"engagement_time",category:"engagement",label:"session_duration",value:Math.round(e/1e3)})}},l=()=>{i=!1,s()},d=()=>{i||(i=!0,r=Date.now())};return["mousedown","mousemove","keypress","scroll","touchstart"].forEach(e=>{document.addEventListener(e,d,{passive:!0})}),window.addEventListener("beforeunload",l),()=>{t.disconnect(),document.removeEventListener("visibilitychange",n),window.removeEventListener("beforeunload",l),["mousedown","mousemove","keypress","scroll","touchstart"].forEach(e=>{document.removeEventListener(e,d)})}},[]),null)},87481:(e,t,a)=>{"use strict";a.d(t,{dj:()=>m});var n=a(12115);let o=0,r=new Map,i=e=>{if(r.has(e))return;let t=setTimeout(()=>{r.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);r.set(e,t)},s=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?i(a):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=s(d,e),l.forEach(e=>{e(d)})}function u(e){let{...t}=e,a=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>c({type:"DISMISS_TOAST",toastId:a});return c({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||n()}}}),{id:a,dismiss:n,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function m(){let[e,t]=n.useState(d);return n.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}}},e=>{var t=t=>e(e.s=t);e.O(0,[838,710,683,983,441,684,358],()=>t(14540)),_N_E=e.O()}]);