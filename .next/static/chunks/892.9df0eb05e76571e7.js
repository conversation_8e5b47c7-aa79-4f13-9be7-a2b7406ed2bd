"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[892],{12486:(e,t,n)=>{n.d(t,{A:()=>l});let l=(0,n(90602).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},14362:(e,t,n)=>{n.d(t,{A:()=>l});let l=(0,n(90602).A)("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},41684:(e,t,n)=>{n.d(t,{A:()=>l});let l=(0,n(90602).A)("Terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]])},60760:(e,t,n)=>{n.d(t,{N:()=>k});var l=n(95155),i=n(12115),r=n(90869),o=n(82885),s=n(97494),a=n(80845),u=n(27351),c=n(51508);class p extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,u.s)(e)&&e.offsetWidth||0,l=this.props.sizeRef.current;l.height=t.offsetHeight||0,l.width=t.offsetWidth||0,l.top=t.offsetTop,l.left=t.offsetLeft,l.right=n-l.width-l.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(e){let{children:t,isPresent:n,anchorX:r}=e,o=(0,i.useId)(),s=(0,i.useRef)(null),a=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,i.useContext)(c.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:t,top:l,left:i,right:c}=a.current;if(n||!s.current||!e||!t)return;s.current.dataset.motionPopId=o;let p=document.createElement("style");return u&&(p.nonce=u),document.head.appendChild(p),p.sheet&&p.sheet.insertRule('\n          [data-motion-pop-id="'.concat(o,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===r?"left: ".concat(i):"right: ".concat(c),"px !important;\n            top: ").concat(l,"px !important;\n          }\n        ")),()=>{document.head.contains(p)&&document.head.removeChild(p)}},[n]),(0,l.jsx)(p,{isPresent:n,childRef:s,sizeRef:a,children:i.cloneElement(t,{ref:s})})}let f=e=>{let{children:t,initial:n,isPresent:r,onExitComplete:s,custom:u,presenceAffectsLayout:c,mode:p,anchorX:f}=e,m=(0,o.M)(h),y=(0,i.useId)(),x=!0,k=(0,i.useMemo)(()=>(x=!1,{id:y,initial:n,isPresent:r,custom:u,onExitComplete:e=>{for(let t of(m.set(e,!0),m.values()))if(!t)return;s&&s()},register:e=>(m.set(e,!1),()=>m.delete(e))}),[r,m,s]);return c&&x&&(k={...k}),(0,i.useMemo)(()=>{m.forEach((e,t)=>m.set(t,!1))},[r]),i.useEffect(()=>{r||m.size||!s||s()},[r]),"popLayout"===p&&(t=(0,l.jsx)(d,{isPresent:r,anchorX:f,children:t})),(0,l.jsx)(a.t.Provider,{value:k,children:t})};function h(){return new Map}var m=n(32082);let y=e=>e.key||"";function x(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let k=e=>{let{children:t,custom:n,initial:a=!0,onExitComplete:u,presenceAffectsLayout:c=!0,mode:p="sync",propagate:d=!1,anchorX:h="left"}=e,[k,g]=(0,m.xQ)(d),E=(0,i.useMemo)(()=>x(t),[t]),w=d&&!k?[]:E.map(y),C=(0,i.useRef)(!0),M=(0,i.useRef)(E),v=(0,o.M)(()=>new Map),[A,R]=(0,i.useState)(E),[z,P]=(0,i.useState)(E);(0,s.E)(()=>{C.current=!1,M.current=E;for(let e=0;e<z.length;e++){let t=y(z[e]);w.includes(t)?v.delete(t):!0!==v.get(t)&&v.set(t,!1)}},[z,w.length,w.join("-")]);let j=[];if(E!==A){let e=[...E];for(let t=0;t<z.length;t++){let n=z[t],l=y(n);w.includes(l)||(e.splice(t,0,n),j.push(n))}return"wait"===p&&j.length&&(e=j),P(x(e)),R(E),null}let{forceRender:I}=(0,i.useContext)(r.L);return(0,l.jsx)(l.Fragment,{children:z.map(e=>{let t=y(e),i=(!d||!!k)&&(E===z||w.includes(t));return(0,l.jsx)(f,{isPresent:i,initial:(!C.current||!!a)&&void 0,custom:n,presenceAffectsLayout:c,mode:p,onExitComplete:i?void 0:()=>{if(!v.has(t))return;v.set(t,!0);let e=!0;v.forEach(t=>{t||(e=!1)}),e&&(null==I||I(),P(M.current),d&&(null==g||g()),u&&u())},anchorX:h,children:e},t)})})}},74311:(e,t,n)=>{n.d(t,{A:()=>l});let l=(0,n(90602).A)("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])}}]);