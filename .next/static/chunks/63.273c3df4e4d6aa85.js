"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[63],{77063:(e,t,i)=>{let r;i.r(t),i.d(t,{CLSThresholds:()=>S,FCPThresholds:()=>L,INPThresholds:()=>R,LCPThresholds:()=>D,TTFBThresholds:()=>$,onCLS:()=>w,onFCP:()=>P,onINP:()=>q,onLCP:()=>O,onTTFB:()=>j});let n=-1,a=e=>{addEventListener("pageshow",t=>{t.persisted&&(n=t.timeStamp,e(t))},!0)},s=(e,t,i,r)=>{let n,a;return s=>{t.value>=0&&(s||r)&&((a=t.value-(n??0))||void 0===n)&&(n=t.value,t.delta=a,t.rating=((e,t)=>e>t[1]?"poor":e>t[0]?"needs-improvement":"good")(t.value,i),e(t))}},o=e=>{requestAnimationFrame(()=>requestAnimationFrame(()=>e()))},l=()=>{let e=performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},d=()=>{let e=l();return e?.activationStart??0},h=(e,t=-1)=>{let i=l(),r="navigate";return n>=0?r="back-forward-cache":i&&(document.prerendering||d()>0?r="prerender":document.wasDiscarded?r="restore":i.type&&(r=i.type.replace(/_/g,"-"))),{name:e,value:t,rating:"good",delta:0,entries:[],id:`v5-${Date.now()}-${Math.floor(0x82f79cd8fff*Math.random())+1e12}`,navigationType:r}},u=new WeakMap;function c(e,t){return u.get(e)||u.set(e,new t),u.get(e)}class m{t;i=0;o=[];h(e){if(e.hadRecentInput)return;let t=this.o[0],i=this.o.at(-1);this.i&&t&&i&&e.startTime-i.startTime<1e3&&e.startTime-t.startTime<5e3?(this.i+=e.value,this.o.push(e)):(this.i=e.value,this.o=[e]),this.t?.(e)}}let p=(e,t,i={})=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){let r=new PerformanceObserver(e=>{Promise.resolve().then(()=>{t(e.getEntries())})});return r.observe({type:e,buffered:!0,...i}),r}}catch{}},v=e=>{let t=!1;return()=>{t||(e(),t=!0)}},f=-1,g=()=>"hidden"!==document.visibilityState||document.prerendering?1/0:0,T=e=>{"hidden"===document.visibilityState&&f>-1&&(f="visibilitychange"===e.type?e.timeStamp:0,C())},y=()=>{addEventListener("visibilitychange",T,!0),addEventListener("prerenderingchange",T,!0)},C=()=>{removeEventListener("visibilitychange",T,!0),removeEventListener("prerenderingchange",T,!0)},b=()=>{if(f<0){let e=d();f=(document.prerendering?void 0:globalThis.performance.getEntriesByType("visibility-state").filter(t=>"hidden"===t.name&&t.startTime>e)[0]?.startTime)??g(),y(),a(()=>{setTimeout(()=>{f=g(),y()})})}return{get firstHiddenTime(){return f}}},E=e=>{document.prerendering?addEventListener("prerenderingchange",()=>e(),!0):e()},L=[1800,3e3],P=(e,t={})=>{E(()=>{let i=b(),r,n=h("FCP"),l=p("paint",e=>{for(let t of e)"first-contentful-paint"===t.name&&(l.disconnect(),t.startTime<i.firstHiddenTime&&(n.value=Math.max(t.startTime-d(),0),n.entries.push(t),r(!0)))});l&&(r=s(e,n,L,t.reportAllChanges),a(i=>{r=s(e,n=h("FCP"),L,t.reportAllChanges),o(()=>{n.value=performance.now()-i.timeStamp,r(!0)})}))})},S=[.1,.25],w=(e,t={})=>{P(v(()=>{let i,r=h("CLS",0),n=c(t,m),l=e=>{for(let t of e)n.h(t);n.i>r.value&&(r.value=n.i,r.entries=n.o,i())},d=p("layout-shift",l);d&&(i=s(e,r,S,t.reportAllChanges),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&(l(d.takeRecords()),i(!0))}),a(()=>{n.i=0,i=s(e,r=h("CLS",0),S,t.reportAllChanges),o(()=>i())}),setTimeout(i))}))},A=0,I=1/0,k=0,M=e=>{for(let t of e)t.interactionId&&(I=Math.min(I,t.interactionId),A=(k=Math.max(k,t.interactionId))?(k-I)/7+1:0)},F=()=>r?A:performance.interactionCount??0,B=()=>{"interactionCount"in performance||r||(r=p("event",M,{type:"event",buffered:!0,durationThreshold:0}))},N=0;class x{u=[];l=new Map;m;p;v(){N=F(),this.u.length=0,this.l.clear()}P(){let e=Math.min(this.u.length-1,Math.floor((F()-N)/50));return this.u[e]}h(e){if(this.m?.(e),!e.interactionId&&"first-input"!==e.entryType)return;let t=this.u.at(-1),i=this.l.get(e.interactionId);if(i||this.u.length<10||e.duration>t.T){if(i?e.duration>i.T?(i.entries=[e],i.T=e.duration):e.duration===i.T&&e.startTime===i.entries[0].startTime&&i.entries.push(e):(i={id:e.interactionId,entries:[e],T:e.duration},this.l.set(i.id,i),this.u.push(i)),this.u.sort((e,t)=>t.T-e.T),this.u.length>10)for(let e of this.u.splice(10))this.l.delete(e.id);this.p?.(i)}}}let _=e=>{let t=globalThis.requestIdleCallback||setTimeout;"hidden"===document.visibilityState?e():(t(e=v(e)),document.addEventListener("visibilitychange",e,{once:!0}))},R=[200,500],q=(e,t={})=>{globalThis.PerformanceEventTiming&&"interactionId"in PerformanceEventTiming.prototype&&E(()=>{B();let i,r=h("INP"),n=c(t,x),o=e=>{_(()=>{for(let t of e)n.h(t);let t=n.P();t&&t.T!==r.value&&(r.value=t.T,r.entries=t.entries,i())})},l=p("event",o,{durationThreshold:t.durationThreshold??40});i=s(e,r,R,t.reportAllChanges),l&&(l.observe({type:"first-input",buffered:!0}),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&(o(l.takeRecords()),i(!0))}),a(()=>{n.v(),i=s(e,r=h("INP"),R,t.reportAllChanges)}))})};class H{m;h(e){this.m?.(e)}}let D=[2500,4e3],O=(e,t={})=>{E(()=>{let i=b(),r,n=h("LCP"),l=c(t,H),u=e=>{for(let a of(t.reportAllChanges||(e=e.slice(-1)),e))l.h(a),a.startTime<i.firstHiddenTime&&(n.value=Math.max(a.startTime-d(),0),n.entries=[a],r())},m=p("largest-contentful-paint",u);if(m){r=s(e,n,D,t.reportAllChanges);let i=v(()=>{u(m.takeRecords()),m.disconnect(),r(!0)});for(let e of["keydown","click","visibilitychange"])addEventListener(e,()=>_(i),{capture:!0,once:!0});a(i=>{r=s(e,n=h("LCP"),D,t.reportAllChanges),o(()=>{n.value=performance.now()-i.timeStamp,r(!0)})})}})},$=[800,1800],W=e=>{document.prerendering?E(()=>W(e)):"complete"!==document.readyState?addEventListener("load",()=>W(e),!0):setTimeout(e)},j=(e,t={})=>{let i=h("TTFB"),r=s(e,i,$,t.reportAllChanges);W(()=>{let n=l();n&&(i.value=Math.max(n.responseStart-d(),0),i.entries=[n],r(!0),a(()=>{(r=s(e,i=h("TTFB",0),$,t.reportAllChanges))(!0)}))})}}}]);