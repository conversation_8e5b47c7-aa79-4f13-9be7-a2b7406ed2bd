"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[840],{34840:(e,t,n)=>{n.r(t),n.d(t,{default:()=>m});var o=n(95155),a=n(12115),s=n(58892),r=n(60760),i=n(41684),l=n(74311),c=n(14362),h=n(54416),u=n(12486);let d={help:["\uD83D\uDCBB Available Commands:","- help: Display this help message","- about: Learn about Green Hacker","- skills: View technical skills","- projects: Show recent projects","- contact: Get contact information","- clear: Clear the terminal","- exit: Close the chatbot","","You can also just chat naturally!"],about:["Hey there! \uD83D\uDC4B I'm <PERSON>, a full-stack developer and ML enthusiast.","When I'm not coding, I'm probably hiking, gaming, or learning something new.","I specialize in creating interactive web experiences and AI-powered applications."],skills:["\uD83D\uDE80 Technical Skills:","- Frontend: React, TypeScript, Tailwind CSS, Framer Motion","- Backend: Node.js, Express, FastAPI, GraphQL","- ML/AI: PyTorch, TensorFlow, Computer Vision","- DevOps: Docker, AWS, CI/CD, Kubernetes","- Other: Three.js, React Three Fiber, WebGL"],projects:["\uD83D\uDCC1 Recent Projects:","1. AI Photo Platform - Face recognition for intelligent photo organization","2. Portfolio Website - You're looking at it right now!","3. ML Research Tool - Natural language processing for scientific papers","4. Real-time Collaboration App - WebRTC and WebSockets for seamless teamwork","",'Type "project [number]" for more details!'],"project 1":["\uD83D\uDCF7 AI Photo Platform","A machine learning application that uses facial recognition to organize and tag photos.","Tech stack: React, TypeScript, PyTorch, AWS S3, Tailwind CSS","Features: Face recognition, automatic tagging, search by person, cloud storage"],"project 2":["\uD83C\uDF10 Portfolio Website","An interactive portfolio showcasing my projects and skills with 3D elements.","Tech stack: React, Three.js, Framer Motion, Tailwind CSS","Features: 3D visualization, interactive components, responsive design"],"project 3":["\uD83D\uDCDA ML Research Tool","An AI-powered tool that helps researchers find relevant papers and extract insights.","Tech stack: Python, TensorFlow, FastAPI, React","Features: Paper recommendation, text summarization, citation network analysis"],"project 4":["\uD83D\uDC65 Real-time Collaboration App","A platform for teams to collaborate with document sharing and real-time editing.","Tech stack: React, Node.js, Socket.io, WebRTC, MongoDB","Features: Live document editing, video chat, project management tools"],contact:["\uD83D\uDCEB Contact Information:","Email: <EMAIL>","GitHub: github.com/greenhacker","LinkedIn: linkedin.com/in/greenhacker","Twitter: @greenhacker"],clear:[""],exit:["\uD83D\uDC4B Goodbye! You can open me again by clicking the terminal icon."]},m=()=>{let[e,t]=(0,a.useState)(!1),[n,m]=(0,a.useState)(!1),[p,g]=(0,a.useState)([{type:"bot",content:["\uD83D\uDC4B Hi there! I'm GREENHACKER's AI assistant.","I can tell you about GREENHACKER, their skills, projects, or how to get in touch.",'Type "help" to see what I can do!']}]),[x,b]=(0,a.useState)(""),[f,y]=(0,a.useState)(!1),w=(0,a.useRef)(null),j=(0,a.useRef)(null);(0,a.useEffect)(()=>{var e;null==(e=w.current)||e.scrollIntoView({behavior:"smooth"})},[p]),(0,a.useEffect)(()=>{if(e){var t;null==(t=j.current)||t.focus()}},[e]);let v=()=>{t(!e)},k=e=>{let n=e.toLowerCase().trim();if("exit"===n){g([...p,{type:"user",content:[e]},{type:"bot",content:d.exit}]),setTimeout(()=>t(!1),1e3);return}return"clear"===n?void g([]):d[n]?void g([...p,{type:"user",content:[e]},{type:"bot",content:d[n]}]):void(g([...p,{type:"user",content:[e]}]),y(!0),setTimeout(async()=>{try{let t=await N(e);g(e=>[...e,{type:"bot",content:t}])}catch(n){console.error("AI response error:",n);let t=A(e);g(e=>[...e,{type:"bot",content:t}])}finally{y(!1)}},1e3+1e3*Math.random()))},N=async e=>{try{let t=await fetch("/api/ai/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:e,context:"Terminal interface - GREENHACKER portfolio inquiry"})});if(!t.ok)throw Error("HTTP error! status: ".concat(t.status));let n=await t.json();if(n.success)return T(n.response);throw Error(n.error||"Failed to get AI response")}catch(t){return console.error("Terminal AI error:",t),A(e)}},T=e=>{let t=e.split(" "),n=[],o="";for(let e of t)o.length+e.length+1<=60?o+=(o?" ":"")+e:(o&&n.push(o),o=e);return o&&n.push(o),n},A=e=>{let t=e.toLowerCase();if(t.includes("hi")||t.includes("hello")||t.includes("hey"))return["Hello! How can I help you today? \uD83D\uDE0A",'Type "help" to see what I can do.'];if(t.includes("thanks")||t.includes("thank you"))return["You're welcome! Anything else you'd like to know?"];if(t.includes("experience")||t.includes("work"))return["GREENHACKER has extensive experience in full-stack","development and machine learning projects.","They've worked on various AI-powered applications."];if(t.includes("education"))return["GREENHACKER has strong technical education and","continuously learns new technologies.","They specialize in AI and web development."];if(t.includes("name"))return["My name is GreenBot! I'm GREENHACKER's AI assistant."];else return["I'm not sure I understand that query.",'Type "help" to see what commands are available.',"Or ask me about GREENHACKER's skills and projects!"]};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(s.P.button,{className:"fixed bottom-8 right-8 bg-neon-green text-black h-12 w-12 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform z-50",whileHover:{scale:1.1},whileTap:{scale:.9},onClick:v,children:(0,o.jsx)(i.A,{size:20})}),(0,o.jsx)(r.N,{children:e&&(0,o.jsxs)(s.P.div,{className:"fixed ".concat(n?"inset-4 md:inset-10":"bottom-24 right-8 w-[350px] md:w-[400px] h-[500px]"," bg-black border border-neon-green/50 rounded-lg shadow-lg overflow-hidden z-50 flex flex-col"),initial:{opacity:0,y:50},animate:{opacity:1,y:0},exit:{opacity:0,y:50},transition:{duration:.3},children:[(0,o.jsxs)("div",{className:"flex items-center justify-between p-3 border-b border-neon-green/30 bg-black",children:[(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)(i.A,{className:"text-neon-green mr-2",size:18}),(0,o.jsx)("h3",{className:"text-neon-green font-mono text-sm",children:"GREENHACKER Terminal"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("button",{className:"text-neon-green hover:text-white transition-colors focus:outline-none",onClick:()=>{m(!n)},children:n?(0,o.jsx)(l.A,{size:16}):(0,o.jsx)(c.A,{size:16})}),(0,o.jsx)("button",{className:"text-neon-green hover:text-white transition-colors focus:outline-none",onClick:v,children:(0,o.jsx)(h.A,{size:16})})]})]}),(0,o.jsx)("div",{className:"flex-grow overflow-y-auto p-4",style:{backgroundColor:"#0d1117"},children:(0,o.jsxs)("div",{className:"space-y-4",children:[p.map((e,t)=>(0,o.jsxs)("div",{className:"".concat("user"===e.type?"ml-auto max-w-[80%]":"mr-auto max-w-[80%]"),children:[(0,o.jsx)("div",{className:"rounded-lg p-3 ".concat("user"===e.type?"bg-neon-green/20 text-white":"bg-github-light text-neon-green"),children:e.content.map((e,t)=>(0,o.jsx)(a.Fragment,{children:""===e?(0,o.jsx)("br",{}):(0,o.jsx)("p",{className:"font-mono text-sm",children:e})},t))}),(0,o.jsxs)("p",{className:"text-xs text-github-text mt-1",children:["user"===e.type?"You":"GREENHACKER Bot"," • ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})]},t)),f&&(0,o.jsx)("div",{className:"mr-auto",children:(0,o.jsx)("div",{className:"bg-github-light rounded-lg p-3 max-w-[80%]",children:(0,o.jsxs)("div",{className:"flex space-x-1",children:[(0,o.jsx)("div",{className:"h-2 w-2 bg-neon-green rounded-full animate-bounce"}),(0,o.jsx)("div",{className:"h-2 w-2 bg-neon-green rounded-full animate-bounce",style:{animationDelay:"0.2s"}}),(0,o.jsx)("div",{className:"h-2 w-2 bg-neon-green rounded-full animate-bounce",style:{animationDelay:"0.4s"}})]})})}),(0,o.jsx)("div",{ref:w})]})}),(0,o.jsx)("form",{onSubmit:e=>{e.preventDefault(),x.trim()&&(k(x),b(""))},className:"p-3 border-t border-neon-green/30 bg-github-dark",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("span",{className:"text-neon-green font-mono mr-2",children:"$"}),(0,o.jsx)("input",{ref:j,type:"text",value:x,onChange:e=>b(e.target.value),className:"flex-grow bg-transparent border-none text-white font-mono focus:outline-none text-sm",placeholder:"Type a message or command..."}),(0,o.jsx)("button",{type:"submit",className:"text-neon-green hover:text-white transition-colors focus:outline-none",children:(0,o.jsx)(u.A,{size:16})})]})})]})})]})}}}]);