"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[436],{8619:(e,t,n)=>{n.d(t,{d:()=>i});var r=n(60098),l=n(12115),s=n(51508),u=n(82885);function i(e){let t=(0,u.M)(()=>(0,r.OQ)(e)),{isStatic:n}=(0,l.useContext)(s.Q);if(n){let[,n]=(0,l.useState)(e);(0,l.useEffect)(()=>t.on("change",n),[])}return t}},37602:(e,t,n)=>{n.d(t,{z:()=>f});var r=n(64803),l=n(30532),s=n(69515);function u(e){return"number"==typeof e?e:parseFloat(e)}var i=n(12115),o=n(51508),a=n(8619),c=n(58829);function f(e,t={}){let{isStatic:n}=(0,i.useContext)(o.Q),d=()=>(0,r.S)(e)?e.get():e;if(n)return(0,c.G)(d);let p=(0,a.d)(d());return(0,i.useInsertionEffect)(()=>(function(e,t,n){let i,o,a=e.get(),c=null,f=a,d="string"==typeof a?a.replace(/[\d.-]/g,""):void 0,p=()=>{c&&(c.stop(),c=null)},v=()=>{p(),c=new l.s({keyframes:[u(e.get()),u(f)],velocity:e.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...n,onUpdate:i})};return e.attach((t,n)=>(f=t,i=e=>{var t,r;return n((t=e,(r=d)?t+r:t))},s.Gt.postRender(v),e.get()),p),(0,r.S)(t)&&(o=t.on("change",t=>{var n,r;return e.set((n=t,(r=d)?n+r:n))}),e.on("destroy",o)),o})(p,e,t),[p,JSON.stringify(t)]),p}},58829:(e,t,n)=>{n.d(t,{G:()=>c});var r=n(6775),l=n(82885),s=n(69515),u=n(97494),i=n(8619);function o(e,t){let n=(0,i.d)(t()),r=()=>n.set(t());return r(),(0,u.E)(()=>{let t=()=>s.Gt.preRender(r,!1,!0),n=e.map(e=>e.on("change",t));return()=>{n.forEach(e=>e()),(0,s.WG)(r)}}),n}var a=n(60098);function c(e,t,n,l){if("function"==typeof e){a.bt.current=[],e();let t=o(a.bt.current,e);return a.bt.current=void 0,t}let s="function"==typeof t?t:function(...e){let t=!Array.isArray(e[0]),n=t?0:-1,l=e[0+n],s=e[1+n],u=e[2+n],i=e[3+n],o=(0,r.G)(s,u,i);return t?o(l):o}(t,n,l);return Array.isArray(e)?f(e,s):f([e],([e])=>s(e))}function f(e,t){let n=(0,l.M)(()=>[]);return o(e,()=>{n.length=0;let r=e.length;for(let t=0;t<r;t++)n[t]=e[t].get();return t(n)})}},74436:(e,t,n)=>{n.r(t),n.d(t,{default:()=>o});var r=n(95155),l=n(12115),s=n(8619),u=n(37602),i=n(58892);let o=()=>{let e=(0,s.d)(0),t=(0,s.d)(0),n={stiffness:50,damping:50},o=(0,u.z)(e,n),a=(0,u.z)(t,n),c=(0,l.useRef)(null);return(0,l.useEffect)(()=>{let n=n=>{if(!c.current)return;let r=c.current.getBoundingClientRect(),l=r.left+r.width/2,s=r.top+r.height/2,u=(n.clientX-l)/(r.width/2),i=(n.clientY-s)/(r.height/2);e.set(10*u),t.set(10*i)};return window.addEventListener("mousemove",n),()=>{window.removeEventListener("mousemove",n)}},[e,t]),(0,r.jsx)("div",{ref:c,className:"fixed inset-0 pointer-events-none z-0 overflow-hidden",children:(0,r.jsxs)(i.P.div,{className:"absolute inset-0 opacity-20",style:{translateX:o,translateY:a},children:[(0,r.jsx)("div",{className:"absolute top-0 -left-4 w-[50vw] h-[50vw] bg-neon-purple rounded-full mix-blend-screen filter blur-[100px] opacity-70"}),(0,r.jsx)("div",{className:"absolute top-[30%] -right-[10%] w-[40vw] h-[40vw] bg-neon-green rounded-full mix-blend-screen filter blur-[100px] opacity-70"}),(0,r.jsx)("div",{className:"absolute -bottom-[20%] left-[20%] w-[60vw] h-[60vw] bg-neon-blue rounded-full mix-blend-screen filter blur-[100px] opacity-40"})]})})}}}]);