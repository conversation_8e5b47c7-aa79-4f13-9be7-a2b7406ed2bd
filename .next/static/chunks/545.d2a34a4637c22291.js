(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[545],{7610:(t,e)=>{e.read=function(t,e,r,n,i){var o,f,u=8*i-n-1,s=(1<<u)-1,h=s>>1,l=-7,a=r?i-1:0,c=r?-1:1,p=t[e+a];for(a+=c,o=p&(1<<-l)-1,p>>=-l,l+=u;l>0;o=256*o+t[e+a],a+=c,l-=8);for(f=o&(1<<-l)-1,o>>=-l,l+=n;l>0;f=256*f+t[e+a],a+=c,l-=8);if(0===o)o=1-h;else{if(o===s)return f?NaN:1/0*(p?-1:1);f+=Math.pow(2,n),o-=h}return(p?-1:1)*f*Math.pow(2,o-n)},e.write=function(t,e,r,n,i,o){var f,u,s,h=8*o-i-1,l=(1<<h)-1,a=l>>1,c=5960464477539062e-23*(23===i),p=n?0:o-1,y=n?1:-1,g=+(e<0||0===e&&1/e<0);for(isNaN(e=Math.abs(e))||e===1/0?(u=+!!isNaN(e),f=l):(f=Math.floor(Math.log(e)/Math.LN2),e*(s=Math.pow(2,-f))<1&&(f--,s*=2),f+a>=1?e+=c/s:e+=c*Math.pow(2,1-a),e*s>=2&&(f++,s/=2),f+a>=l?(u=0,f=l):f+a>=1?(u=(e*s-1)*Math.pow(2,i),f+=a):(u=e*Math.pow(2,a-1)*Math.pow(2,i),f=0));i>=8;t[r+p]=255&u,p+=y,u/=256,i-=8);for(f=f<<i|u,h+=i;h>0;t[r+p]=255&f,p+=y,f/=256,h-=8);t[r+p-y]|=128*g}},40501:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>l});var n=r(95155),i=r(12115),o=r(78975),f=r(54052);let u=[],s={width:"100%",height:"100%"},h=(0,i.forwardRef)(function(t,e){var r;let{className:o,children:h,debounceTime:l=300,ignoreDimensions:a=u,parentSizeStyles:c,enableDebounceLeadingCall:p=!0,resizeObserverPolyfill:y,...g}=t,d=(0,i.useRef)(null),b=(0,i.useRef)(0),[m,w]=(0,i.useState)({width:0,height:0,top:0,left:0}),E=(0,i.useMemo)(()=>{let t=Array.isArray(a)?a:[a];return f(e=>{w(r=>Object.keys(r).filter(t=>r[t]!==e[t]).every(e=>t.includes(e))?r:e)},l,{leading:p})},[l,p,a]);return(0,i.useEffect)(()=>{let t=new(y||window.ResizeObserver)(t=>{t.forEach(t=>{var e;let{left:r,top:n,width:i,height:o}=null!=(e=null==t?void 0:t.contentRect)?e:{};b.current=window.requestAnimationFrame(()=>{E({width:i,height:o,top:n,left:r})})})});return d.current&&t.observe(d.current),()=>{window.cancelAnimationFrame(b.current),t.disconnect(),E.cancel()}},[E,y]),(0,n.jsx)("div",{style:{...s,...c},ref:(r=[e,d],t=>{r.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}),className:o,...g,children:h({...m,ref:d.current,resize:E})})}),l=(0,i.forwardRef)((t,e)=>{let{scene:r,style:f,onSplineMouseDown:u,onSplineMouseUp:s,onSplineMouseHover:l,onSplineKeyDown:a,onSplineKeyUp:c,onSplineStart:p,onSplineLookAt:y,onSplineFollow:g,onSplineScroll:d,onLoad:b,renderOnDemand:m=!0,children:w,...E}=t,B=(0,i.useRef)(null),[v,A]=(0,i.useState)(!0),[I,U]=(0,i.useState)();if(I)throw I;return(0,i.useEffect)(()=>{let t;A(!0);let e=[{name:"mouseDown",cb:u},{name:"mouseUp",cb:s},{name:"mouseHover",cb:l},{name:"keyDown",cb:a},{name:"keyUp",cb:c},{name:"start",cb:p},{name:"lookAt",cb:y},{name:"follow",cb:g},{name:"scroll",cb:d}];return B.current&&(t=new o.l(B.current,{renderOnDemand:m}),(async function(){for(let n of(await t.load(r),e))n.cb&&t.addEventListener(n.name,n.cb);A(!1),null==b||b(t)})().catch(t=>{U(t)})),()=>{for(let r of e)r.cb&&t.removeEventListener(r.name,r.cb);t.dispose()}},[r]),(0,n.jsx)(h,{ref:e,parentSizeStyles:{overflow:"hidden",...f},debounceTime:50,...E,children:()=>(0,n.jsxs)(n.Fragment,{children:[v&&w,(0,n.jsx)("canvas",{ref:B,style:{display:v?"none":"block"}})]})})})},44134:(t,e,r)=>{"use strict";let n=r(57719),i=r(7610),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function f(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');let e=new Uint8Array(t);return Object.setPrototypeOf(e,u.prototype),e}function u(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return l(t)}return s(t,e,r)}function s(t,e,r){if("string"==typeof t){var n=t,i=e;if(("string"!=typeof i||""===i)&&(i="utf8"),!u.isEncoding(i))throw TypeError("Unknown encoding: "+i);let r=0|y(n,i),o=f(r),s=o.write(n,i);return s!==r&&(o=o.slice(0,s)),o}if(ArrayBuffer.isView(t)){var o=t;if(k(o,Uint8Array)){let t=new Uint8Array(o);return c(t.buffer,t.byteOffset,t.byteLength)}return a(o)}if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(k(t,ArrayBuffer)||t&&k(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(k(t,SharedArrayBuffer)||t&&k(t.buffer,SharedArrayBuffer)))return c(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');let s=t.valueOf&&t.valueOf();if(null!=s&&s!==t)return u.from(s,e,r);let h=function(t){if(u.isBuffer(t)){let e=0|p(t.length),r=f(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?f(0):a(t):"Buffer"===t.type&&Array.isArray(t.data)?a(t.data):void 0}(t);if(h)return h;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return u.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function h(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function l(t){return h(t),f(t<0?0:0|p(t))}function a(t){let e=t.length<0?0:0|p(t.length),r=f(e);for(let n=0;n<e;n+=1)r[n]=255&t[n];return r}function c(t,e,r){let n;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),u.prototype),n}function p(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function y(t,e){if(u.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||k(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);let r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;let i=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return C(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return j(t).length;default:if(i)return n?-1:C(t).length;e=(""+e).toLowerCase(),i=!0}}function g(t,e,r){let i=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){let n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);let i="";for(let n=e;n<r;++n)i+=N[t[n]];return i}(this,e,r);case"utf8":case"utf-8":return w(this,e,r);case"ascii":return function(t,e,r){let n="";r=Math.min(t.length,r);for(let i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}(this,e,r);case"latin1":case"binary":return function(t,e,r){let n="";r=Math.min(t.length,r);for(let i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}(this,e,r);case"base64":var o,f,u;return o=this,f=e,u=r,0===f&&u===o.length?n.fromByteArray(o):n.fromByteArray(o.slice(f,u));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){let n=t.slice(e,r),i="";for(let t=0;t<n.length-1;t+=2)i+=String.fromCharCode(n[t]+256*n[t+1]);return i}(this,e,r);default:if(i)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),i=!0}}function d(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}function b(t,e,r,n,i){var o;if(0===t.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(o=r*=1)!=o&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length)if(i)return -1;else r=t.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof e&&(e=u.from(e,n)),u.isBuffer(e))return 0===e.length?-1:m(t,e,r,n,i);if("number"==typeof e){if(e&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(t,e,r);else return Uint8Array.prototype.lastIndexOf.call(t,e,r);return m(t,[e],r,n,i)}throw TypeError("val must be string, number or Buffer")}function m(t,e,r,n,i){let o,f=1,u=t.length,s=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return -1;f=2,u/=2,s/=2,r/=2}function h(t,e){return 1===f?t[e]:t.readUInt16BE(e*f)}if(i){let n=-1;for(o=r;o<u;o++)if(h(t,o)===h(e,-1===n?0:o-n)){if(-1===n&&(n=o),o-n+1===s)return n*f}else -1!==n&&(o-=o-n),n=-1}else for(r+s>u&&(r=u-s),o=r;o>=0;o--){let r=!0;for(let n=0;n<s;n++)if(h(t,o+n)!==h(e,n)){r=!1;break}if(r)return o}return -1}function w(t,e,r){r=Math.min(t.length,r);let n=[],i=e;for(;i<r;){let e=t[i],o=null,f=e>239?4:e>223?3:e>191?2:1;if(i+f<=r){let r,n,u,s;switch(f){case 1:e<128&&(o=e);break;case 2:(192&(r=t[i+1]))==128&&(s=(31&e)<<6|63&r)>127&&(o=s);break;case 3:r=t[i+1],n=t[i+2],(192&r)==128&&(192&n)==128&&(s=(15&e)<<12|(63&r)<<6|63&n)>2047&&(s<55296||s>57343)&&(o=s);break;case 4:r=t[i+1],n=t[i+2],u=t[i+3],(192&r)==128&&(192&n)==128&&(192&u)==128&&(s=(15&e)<<18|(63&r)<<12|(63&n)<<6|63&u)>65535&&s<1114112&&(o=s)}}null===o?(o=65533,f=1):o>65535&&(o-=65536,n.push(o>>>10&1023|55296),o=56320|1023&o),n.push(o),i+=f}var o=n;let f=o.length;if(f<=4096)return String.fromCharCode.apply(String,o);let u="",s=0;for(;s<f;)u+=String.fromCharCode.apply(String,o.slice(s,s+=4096));return u}function E(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function B(t,e,r,n,i,o){if(!u.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw RangeError('"value" argument is out of bounds');if(r+n>t.length)throw RangeError("Index out of range")}function v(t,e,r,n,i){T(e,n,i,t,r,7);let o=Number(e&BigInt(0xffffffff));t[r++]=o,o>>=8,t[r++]=o,o>>=8,t[r++]=o,o>>=8,t[r++]=o;let f=Number(e>>BigInt(32)&BigInt(0xffffffff));return t[r++]=f,f>>=8,t[r++]=f,f>>=8,t[r++]=f,f>>=8,t[r++]=f,r}function A(t,e,r,n,i){T(e,n,i,t,r,7);let o=Number(e&BigInt(0xffffffff));t[r+7]=o,o>>=8,t[r+6]=o,o>>=8,t[r+5]=o,o>>=8,t[r+4]=o;let f=Number(e>>BigInt(32)&BigInt(0xffffffff));return t[r+3]=f,f>>=8,t[r+2]=f,f>>=8,t[r+1]=f,f>>=8,t[r]=f,r+8}function I(t,e,r,n,i,o){if(r+n>t.length||r<0)throw RangeError("Index out of range")}function U(t,e,r,n,o){return e*=1,r>>>=0,o||I(t,e,r,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,r,n,23,4),r+4}function x(t,e,r,n,o){return e*=1,r>>>=0,o||I(t,e,r,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,r,n,52,8),r+8}e.hp=u,e.IS=50,u.TYPED_ARRAY_SUPPORT=function(){try{let t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),u.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(u.prototype,"parent",{enumerable:!0,get:function(){if(u.isBuffer(this))return this.buffer}}),Object.defineProperty(u.prototype,"offset",{enumerable:!0,get:function(){if(u.isBuffer(this))return this.byteOffset}}),u.poolSize=8192,u.from=function(t,e,r){return s(t,e,r)},Object.setPrototypeOf(u.prototype,Uint8Array.prototype),Object.setPrototypeOf(u,Uint8Array),u.alloc=function(t,e,r){return(h(t),t<=0)?f(t):void 0!==e?"string"==typeof r?f(t).fill(e,r):f(t).fill(e):f(t)},u.allocUnsafe=function(t){return l(t)},u.allocUnsafeSlow=function(t){return l(t)},u.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==u.prototype},u.compare=function(t,e){if(k(t,Uint8Array)&&(t=u.from(t,t.offset,t.byteLength)),k(e,Uint8Array)&&(e=u.from(e,e.offset,e.byteLength)),!u.isBuffer(t)||!u.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;let r=t.length,n=e.length;for(let i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:+(n<r)},u.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(t,e){let r;if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return u.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;let n=u.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){let e=t[r];if(k(e,Uint8Array))i+e.length>n.length?(u.isBuffer(e)||(e=u.from(e)),e.copy(n,i)):Uint8Array.prototype.set.call(n,e,i);else if(u.isBuffer(e))e.copy(n,i);else throw TypeError('"list" argument must be an Array of Buffers');i+=e.length}return n},u.byteLength=y,u.prototype._isBuffer=!0,u.prototype.swap16=function(){let t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(let e=0;e<t;e+=2)d(this,e,e+1);return this},u.prototype.swap32=function(){let t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(let e=0;e<t;e+=4)d(this,e,e+3),d(this,e+1,e+2);return this},u.prototype.swap64=function(){let t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(let e=0;e<t;e+=8)d(this,e,e+7),d(this,e+1,e+6),d(this,e+2,e+5),d(this,e+3,e+4);return this},u.prototype.toString=function(){let t=this.length;return 0===t?"":0==arguments.length?w(this,0,t):g.apply(this,arguments)},u.prototype.toLocaleString=u.prototype.toString,u.prototype.equals=function(t){if(!u.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===u.compare(this,t)},u.prototype.inspect=function(){let t="",r=e.IS;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},o&&(u.prototype[o]=u.prototype.inspect),u.prototype.compare=function(t,e,r,n,i){if(k(t,Uint8Array)&&(t=u.from(t,t.offset,t.byteLength)),!u.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,i>>>=0,this===t)return 0;let o=i-n,f=r-e,s=Math.min(o,f),h=this.slice(n,i),l=t.slice(e,r);for(let t=0;t<s;++t)if(h[t]!==l[t]){o=h[t],f=l[t];break}return o<f?-1:+(f<o)},u.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},u.prototype.indexOf=function(t,e,r){return b(this,t,e,r,!0)},u.prototype.lastIndexOf=function(t,e,r){return b(this,t,e,r,!1)},u.prototype.write=function(t,e,r,n){var i,o,f,u,s,h,l,a;if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let c=this.length-e;if((void 0===r||r>c)&&(r=c),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");let p=!1;for(;;)switch(n){case"hex":return function(t,e,r,n){let i;r=Number(r)||0;let o=t.length-r;n?(n=Number(n))>o&&(n=o):n=o;let f=e.length;for(n>f/2&&(n=f/2),i=0;i<n;++i){var u;let n=parseInt(e.substr(2*i,2),16);if((u=n)!=u)break;t[r+i]=n}return i}(this,t,e,r);case"utf8":case"utf-8":return i=e,o=r,M(C(t,this.length-i),this,i,o);case"ascii":case"latin1":case"binary":return f=e,u=r,M(function(t){let e=[];for(let r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}(t),this,f,u);case"base64":return s=e,h=r,M(j(t),this,s,h);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return l=e,a=r,M(function(t,e){let r,n,i=[];for(let o=0;o<t.length&&!((e-=2)<0);++o)n=(r=t.charCodeAt(o))>>8,i.push(r%256),i.push(n);return i}(t,this.length-l),this,l,a);default:if(p)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),p=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},u.prototype.slice=function(t,e){let r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);let n=this.subarray(t,e);return Object.setPrototypeOf(n,u.prototype),n},u.prototype.readUintLE=u.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||E(t,e,this.length);let n=this[t],i=1,o=0;for(;++o<e&&(i*=256);)n+=this[t+o]*i;return n},u.prototype.readUintBE=u.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||E(t,e,this.length);let n=this[t+--e],i=1;for(;e>0&&(i*=256);)n+=this[t+--e]*i;return n},u.prototype.readUint8=u.prototype.readUInt8=function(t,e){return t>>>=0,e||E(t,1,this.length),this[t]},u.prototype.readUint16LE=u.prototype.readUInt16LE=function(t,e){return t>>>=0,e||E(t,2,this.length),this[t]|this[t+1]<<8},u.prototype.readUint16BE=u.prototype.readUInt16BE=function(t,e){return t>>>=0,e||E(t,2,this.length),this[t]<<8|this[t+1]},u.prototype.readUint32LE=u.prototype.readUInt32LE=function(t,e){return t>>>=0,e||E(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},u.prototype.readUint32BE=u.prototype.readUInt32BE=function(t,e){return t>>>=0,e||E(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},u.prototype.readBigUInt64LE=P(function(t){L(t>>>=0,"offset");let e=this[t],r=this[t+7];(void 0===e||void 0===r)&&_(t,this.length-8);let n=e+256*this[++t]+65536*this[++t]+0x1000000*this[++t],i=this[++t]+256*this[++t]+65536*this[++t]+0x1000000*r;return BigInt(n)+(BigInt(i)<<BigInt(32))}),u.prototype.readBigUInt64BE=P(function(t){L(t>>>=0,"offset");let e=this[t],r=this[t+7];(void 0===e||void 0===r)&&_(t,this.length-8);let n=0x1000000*e+65536*this[++t]+256*this[++t]+this[++t],i=0x1000000*this[++t]+65536*this[++t]+256*this[++t]+r;return(BigInt(n)<<BigInt(32))+BigInt(i)}),u.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||E(t,e,this.length);let n=this[t],i=1,o=0;for(;++o<e&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},u.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||E(t,e,this.length);let n=e,i=1,o=this[t+--n];for(;n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},u.prototype.readInt8=function(t,e){return(t>>>=0,e||E(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},u.prototype.readInt16LE=function(t,e){t>>>=0,e||E(t,2,this.length);let r=this[t]|this[t+1]<<8;return 32768&r?0xffff0000|r:r},u.prototype.readInt16BE=function(t,e){t>>>=0,e||E(t,2,this.length);let r=this[t+1]|this[t]<<8;return 32768&r?0xffff0000|r:r},u.prototype.readInt32LE=function(t,e){return t>>>=0,e||E(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},u.prototype.readInt32BE=function(t,e){return t>>>=0,e||E(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},u.prototype.readBigInt64LE=P(function(t){L(t>>>=0,"offset");let e=this[t],r=this[t+7];return(void 0===e||void 0===r)&&_(t,this.length-8),(BigInt(this[t+4]+256*this[t+5]+65536*this[t+6]+(r<<24))<<BigInt(32))+BigInt(e+256*this[++t]+65536*this[++t]+0x1000000*this[++t])}),u.prototype.readBigInt64BE=P(function(t){L(t>>>=0,"offset");let e=this[t],r=this[t+7];return(void 0===e||void 0===r)&&_(t,this.length-8),(BigInt((e<<24)+65536*this[++t]+256*this[++t]+this[++t])<<BigInt(32))+BigInt(0x1000000*this[++t]+65536*this[++t]+256*this[++t]+r)}),u.prototype.readFloatLE=function(t,e){return t>>>=0,e||E(t,4,this.length),i.read(this,t,!0,23,4)},u.prototype.readFloatBE=function(t,e){return t>>>=0,e||E(t,4,this.length),i.read(this,t,!1,23,4)},u.prototype.readDoubleLE=function(t,e){return t>>>=0,e||E(t,8,this.length),i.read(this,t,!0,52,8)},u.prototype.readDoubleBE=function(t,e){return t>>>=0,e||E(t,8,this.length),i.read(this,t,!1,52,8)},u.prototype.writeUintLE=u.prototype.writeUIntLE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){let n=Math.pow(2,8*r)-1;B(this,t,e,r,n,0)}let i=1,o=0;for(this[e]=255&t;++o<r&&(i*=256);)this[e+o]=t/i&255;return e+r},u.prototype.writeUintBE=u.prototype.writeUIntBE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){let n=Math.pow(2,8*r)-1;B(this,t,e,r,n,0)}let i=r-1,o=1;for(this[e+i]=255&t;--i>=0&&(o*=256);)this[e+i]=t/o&255;return e+r},u.prototype.writeUint8=u.prototype.writeUInt8=function(t,e,r){return t*=1,e>>>=0,r||B(this,t,e,1,255,0),this[e]=255&t,e+1},u.prototype.writeUint16LE=u.prototype.writeUInt16LE=function(t,e,r){return t*=1,e>>>=0,r||B(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},u.prototype.writeUint16BE=u.prototype.writeUInt16BE=function(t,e,r){return t*=1,e>>>=0,r||B(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},u.prototype.writeUint32LE=u.prototype.writeUInt32LE=function(t,e,r){return t*=1,e>>>=0,r||B(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},u.prototype.writeUint32BE=u.prototype.writeUInt32BE=function(t,e,r){return t*=1,e>>>=0,r||B(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},u.prototype.writeBigUInt64LE=P(function(t,e=0){return v(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),u.prototype.writeBigUInt64BE=P(function(t,e=0){return A(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),u.prototype.writeIntLE=function(t,e,r,n){if(t*=1,e>>>=0,!n){let n=Math.pow(2,8*r-1);B(this,t,e,r,n-1,-n)}let i=0,o=1,f=0;for(this[e]=255&t;++i<r&&(o*=256);)t<0&&0===f&&0!==this[e+i-1]&&(f=1),this[e+i]=(t/o|0)-f&255;return e+r},u.prototype.writeIntBE=function(t,e,r,n){if(t*=1,e>>>=0,!n){let n=Math.pow(2,8*r-1);B(this,t,e,r,n-1,-n)}let i=r-1,o=1,f=0;for(this[e+i]=255&t;--i>=0&&(o*=256);)t<0&&0===f&&0!==this[e+i+1]&&(f=1),this[e+i]=(t/o|0)-f&255;return e+r},u.prototype.writeInt8=function(t,e,r){return t*=1,e>>>=0,r||B(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},u.prototype.writeInt16LE=function(t,e,r){return t*=1,e>>>=0,r||B(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},u.prototype.writeInt16BE=function(t,e,r){return t*=1,e>>>=0,r||B(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},u.prototype.writeInt32LE=function(t,e,r){return t*=1,e>>>=0,r||B(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},u.prototype.writeInt32BE=function(t,e,r){return t*=1,e>>>=0,r||B(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},u.prototype.writeBigInt64LE=P(function(t,e=0){return v(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),u.prototype.writeBigInt64BE=P(function(t,e=0){return A(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),u.prototype.writeFloatLE=function(t,e,r){return U(this,t,e,!0,r)},u.prototype.writeFloatBE=function(t,e,r){return U(this,t,e,!1,r)},u.prototype.writeDoubleLE=function(t,e,r){return x(this,t,e,!0,r)},u.prototype.writeDoubleBE=function(t,e,r){return x(this,t,e,!1,r)},u.prototype.copy=function(t,e,r,n){if(!u.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);let i=n-r;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,r,n):Uint8Array.prototype.set.call(t,this.subarray(r,n),e),i},u.prototype.fill=function(t,e,r,n){let i;if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!u.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===t.length){let e=t.charCodeAt(0);("utf8"===n&&e<128||"latin1"===n)&&(t=e)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{let o=u.isBuffer(t)?t:u.from(t,n),f=o.length;if(0===f)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=o[i%f]}return this};let R={};function O(t,e,r){R[t]=class extends r{constructor(){super(),Object.defineProperty(this,"message",{value:e.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${t}]`,this.stack,delete this.name}get code(){return t}set code(t){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:t,writable:!0})}toString(){return`${this.name} [${t}]: ${this.message}`}}}function S(t){let e="",r=t.length,n=+("-"===t[0]);for(;r>=n+4;r-=3)e=`_${t.slice(r-3,r)}${e}`;return`${t.slice(0,r)}${e}`}function T(t,e,r,n,i,o){if(t>r||t<e){let n,i="bigint"==typeof e?"n":"";throw n=o>3?0===e||e===BigInt(0)?`>= 0${i} and < 2${i} ** ${(o+1)*8}${i}`:`>= -(2${i} ** ${(o+1)*8-1}${i}) and < 2 ** ${(o+1)*8-1}${i}`:`>= ${e}${i} and <= ${r}${i}`,new R.ERR_OUT_OF_RANGE("value",n,t)}L(i,"offset"),(void 0===n[i]||void 0===n[i+o])&&_(i,n.length-(o+1))}function L(t,e){if("number"!=typeof t)throw new R.ERR_INVALID_ARG_TYPE(e,"number",t)}function _(t,e,r){if(Math.floor(t)!==t)throw L(t,r),new R.ERR_OUT_OF_RANGE(r||"offset","an integer",t);if(e<0)throw new R.ERR_BUFFER_OUT_OF_BOUNDS;throw new R.ERR_OUT_OF_RANGE(r||"offset",`>= ${+!!r} and <= ${e}`,t)}O("ERR_BUFFER_OUT_OF_BOUNDS",function(t){return t?`${t} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),O("ERR_INVALID_ARG_TYPE",function(t,e){return`The "${t}" argument must be of type number. Received type ${typeof e}`},TypeError),O("ERR_OUT_OF_RANGE",function(t,e,r){let n=`The value of "${t}" is out of range.`,i=r;return Number.isInteger(r)&&Math.abs(r)>0x100000000?i=S(String(r)):"bigint"==typeof r&&(i=String(r),(r>BigInt(2)**BigInt(32)||r<-(BigInt(2)**BigInt(32)))&&(i=S(i)),i+="n"),n+=` It must be ${e}. Received ${i}`},RangeError);let $=/[^+/0-9A-Za-z-_]/g;function C(t,e){let r;e=e||1/0;let n=t.length,i=null,o=[];for(let f=0;f<n;++f){if((r=t.charCodeAt(f))>55295&&r<57344){if(!i){if(r>56319||f+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return o}function j(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace($,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function M(t,e,r,n){let i;for(i=0;i<n&&!(i+r>=e.length)&&!(i>=t.length);++i)e[i+r]=t[i];return i}function k(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}let N=function(){let t="0123456789abcdef",e=Array(256);for(let r=0;r<16;++r){let n=16*r;for(let i=0;i<16;++i)e[n+i]=t[r]+t[i]}return e}();function P(t){return"undefined"==typeof BigInt?F:t}function F(){throw Error("BigInt not supported")}},54052:(t,e,r)=>{var n=0/0,i=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,f=/^0b[01]+$/i,u=/^0o[0-7]+$/i,s=parseInt,h="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g,l="object"==typeof self&&self&&self.Object===Object&&self,a=h||l||Function("return this")(),c=Object.prototype.toString,p=Math.max,y=Math.min,g=function(){return a.Date.now()};function d(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function b(t){if("number"==typeof t)return t;if("symbol"==typeof(e=t)||e&&"object"==typeof e&&"[object Symbol]"==c.call(e))return n;if(d(t)){var e,r="function"==typeof t.valueOf?t.valueOf():t;t=d(r)?r+"":r}if("string"!=typeof t)return 0===t?t:+t;t=t.replace(i,"");var h=f.test(t);return h||u.test(t)?s(t.slice(2),h?2:8):o.test(t)?n:+t}t.exports=function(t,e,r){var n,i,o,f,u,s,h=0,l=!1,a=!1,c=!0;if("function"!=typeof t)throw TypeError("Expected a function");function m(e){var r=n,o=i;return n=i=void 0,h=e,f=t.apply(o,r)}function w(t){var r=t-s,n=t-h;return void 0===s||r>=e||r<0||a&&n>=o}function E(){var t,r,n,i=g();if(w(i))return B(i);u=setTimeout(E,(t=i-s,r=i-h,n=e-t,a?y(n,o-r):n))}function B(t){return(u=void 0,c&&n)?m(t):(n=i=void 0,f)}function v(){var t,r=g(),o=w(r);if(n=arguments,i=this,s=r,o){if(void 0===u)return h=t=s,u=setTimeout(E,e),l?m(t):f;if(a)return u=setTimeout(E,e),m(s)}return void 0===u&&(u=setTimeout(E,e)),f}return e=b(e)||0,d(r)&&(l=!!r.leading,o=(a="maxWait"in r)?p(b(r.maxWait)||0,e):o,c="trailing"in r?!!r.trailing:c),v.cancel=function(){void 0!==u&&clearTimeout(u),h=0,n=s=i=u=void 0},v.flush=function(){return void 0===u?f:B(g())},v}},57719:(t,e)=>{"use strict";e.byteLength=function(t){var e=s(t),r=e[0],n=e[1];return(r+n)*3/4-n},e.toByteArray=function(t){var e,r,o=s(t),f=o[0],u=o[1],h=new i((f+u)*3/4-u),l=0,a=u>0?f-4:f;for(r=0;r<a;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],h[l++]=e>>16&255,h[l++]=e>>8&255,h[l++]=255&e;return 2===u&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,h[l++]=255&e),1===u&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,h[l++]=e>>8&255,h[l++]=255&e),h},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,o=[],f=0,u=n-i;f<u;f+=16383)o.push(function(t,e,n){for(var i,o=[],f=e;f<n;f+=3)i=(t[f]<<16&0xff0000)+(t[f+1]<<8&65280)+(255&t[f+2]),o.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return o.join("")}(t,f,f+16383>u?u:f+16383));return 1===i?o.push(r[(e=t[n-1])>>2]+r[e<<4&63]+"=="):2===i&&o.push(r[(e=(t[n-2]<<8)+t[n-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",f=0,u=o.length;f<u;++f)r[f]=o[f],n[o.charCodeAt(f)]=f;function s(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}n[45]=62,n[95]=63}}]);