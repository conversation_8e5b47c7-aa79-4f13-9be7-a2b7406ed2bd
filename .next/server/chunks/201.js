exports.id=201,exports.ids=[201],exports.modules={4780:(e,t,o)=>{"use strict";o.d(t,{cn:()=>s});var r=o(49384),n=o(82348);function s(...e){return(0,n.QP)((0,r.$)(e))}},20144:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,86346,23)),Promise.resolve().then(o.t.bind(o,27924,23)),Promise.resolve().then(o.t.bind(o,35656,23)),Promise.resolve().then(o.t.bind(o,40099,23)),Promise.resolve().then(o.t.bind(o,38243,23)),Promise.resolve().then(o.t.bind(o,28827,23)),Promise.resolve().then(o.t.bind(o,62763,23)),Promise.resolve().then(o.t.bind(o,97173,23))},20708:(e,t,o)=>{Promise.resolve().then(o.bind(o,29519)),Promise.resolve().then(o.bind(o,92450)),Promise.resolve().then(o.bind(o,30980)),Promise.resolve().then(o.bind(o,26785))},25709:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>g});var r=o(60687),n=o(85814),s=o.n(n),a=o(72244),i=o(32192),l=o(99270),c=o(28559),d=o(43210),p=o(8730),m=o(24224),u=o(4780);let f=(0,m.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),h=d.forwardRef(({className:e,variant:t,size:o,asChild:n=!1,...s},a)=>{let i=n?p.DX:"button";return(0,r.jsx)(i,{className:(0,u.cn)(f({variant:t,size:o,className:e})),ref:a,...s})});function g(){return(0,r.jsx)("div",{className:"min-h-screen bg-github-dark text-github-text flex items-center justify-center",children:(0,r.jsx)("div",{className:"max-w-md mx-auto text-center px-6",children:(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,r.jsx)(a.P.div,{className:"text-8xl font-bold text-neon-green mb-8",initial:{scale:.5},animate:{scale:1},transition:{duration:.5,type:"spring",stiffness:200},children:"404"}),(0,r.jsx)(a.P.h1,{className:"text-3xl font-bold text-white mb-4",initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:"Page Not Found"}),(0,r.jsx)(a.P.p,{className:"text-github-text mb-8 leading-relaxed",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:"Oops! The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL."}),(0,r.jsxs)(a.P.div,{className:"flex flex-col sm:flex-row gap-4 justify-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:[(0,r.jsx)(h,{asChild:!0,className:"bg-neon-green text-black hover:bg-neon-green/90",children:(0,r.jsxs)(s(),{href:"/",className:"flex items-center gap-2",children:[(0,r.jsx)(i.A,{size:18}),"Go Home"]})}),(0,r.jsx)(h,{variant:"outline",asChild:!0,className:"border-github-border text-github-text hover:bg-github-light",children:(0,r.jsxs)(s(),{href:"/#contact",className:"flex items-center gap-2",children:[(0,r.jsx)(l.A,{size:18}),"Contact Support"]})})]}),(0,r.jsx)(a.P.div,{className:"mt-8",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:(0,r.jsxs)(h,{variant:"ghost",onClick:()=>window.history.back(),className:"text-github-text hover:text-white flex items-center gap-2",children:[(0,r.jsx)(c.A,{size:18}),"Go Back"]})}),(0,r.jsx)(a.P.div,{className:"absolute top-1/4 left-1/4 w-2 h-2 bg-neon-green rounded-full opacity-50",animate:{scale:[1,1.5,1],opacity:[.5,1,.5]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}}),(0,r.jsx)(a.P.div,{className:"absolute top-1/3 right-1/4 w-1 h-1 bg-neon-blue rounded-full opacity-50",animate:{scale:[1,2,1],opacity:[.3,.8,.3]},transition:{duration:3,repeat:1/0,ease:"easeInOut",delay:1}})]})})})}h.displayName="Button"},26768:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,16444,23)),Promise.resolve().then(o.t.bind(o,16042,23)),Promise.resolve().then(o.t.bind(o,88170,23)),Promise.resolve().then(o.t.bind(o,49477,23)),Promise.resolve().then(o.t.bind(o,29345,23)),Promise.resolve().then(o.t.bind(o,12089,23)),Promise.resolve().then(o.t.bind(o,46577,23)),Promise.resolve().then(o.t.bind(o,31307,23))},26785:(e,t,o)=>{"use strict";o.d(t,{default:()=>r});let r=(0,o(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/seo/StructuredData.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/seo/StructuredData.tsx","default")},29519:(e,t,o)=>{"use strict";o.d(t,{Providers:()=>r});let r=(0,o(12907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/providers.tsx","Providers")},29867:(e,t,o)=>{"use strict";o.d(t,{dj:()=>m});var r=o(43210);let n=0,s=new Map,a=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:o}=t;return o?a(o):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===o||void 0===o?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],c={toasts:[]};function d(e){c=i(c,e),l.forEach(e=>{e(c)})}function p({...e}){let t=(n=(n+1)%Number.MAX_SAFE_INTEGER).toString(),o=()=>d({type:"DISMISS_TOAST",toastId:t});return d({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||o()}}}),{id:t,dismiss:o,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function m(){let[e,t]=r.useState(c);return r.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:p,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},30980:(e,t,o)=>{"use strict";o.d(t,{default:()=>r});let r=(0,o(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/performance/WebVitals.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/performance/WebVitals.tsx","default")},39612:(e,t,o)=>{Promise.resolve().then(o.bind(o,25709))},46740:(e,t,o)=>{"use strict";o.d(t,{Yi:()=>a,default:()=>l,sx:()=>i});var r=o(60687),n=o(43210),s=o(72600);let a={resumeView:{action:"view_resume",category:"resume",label:"pdf_preview"},resumeDownload:{action:"download_resume",category:"resume",label:"pdf_download"},resumeError:{action:"resume_error",category:"resume",label:"pdf_load_failed"},contactFormSubmit:{action:"submit_form",category:"contact",label:"contact_form"},projectView:{action:"view_project",category:"projects",label:"project_details"},projectLink:{action:"click_project_link",category:"projects",label:"external_link"},githubStatsView:{action:"view_github_stats",category:"github",label:"stats_display"},aiChatStart:{action:"start_chat",category:"ai_chat",label:"terminal_chat"},aiChatMessage:{action:"send_message",category:"ai_chat",label:"user_message"},threeDInteraction:{action:"interact_3d",category:"3d_elements",label:"canvas_interaction"},sectionView:{action:"view_section",category:"navigation",label:"scroll_to_section"}},i=e=>{},l=({measurementId:e=process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID})=>((0,n.useEffect)(()=>{if(!e)return void console.warn("Google Analytics measurement ID not provided");window.gtag=window.gtag||function(){window.gtag.q=window.gtag.q||[],window.gtag.q.push(arguments)},window.gtag("js",new Date),window.gtag("config",e,{page_title:"GreenHacker Portfolio",page_location:window.location.href,send_page_view:!0,enhanced_measurement:{scrolls:!0,outbound_clicks:!0,site_search:!0,video_engagement:!0,file_downloads:!0},custom_map:{custom_parameter_1:"section_name",custom_parameter_2:"interaction_type"}}),i({action:"page_view",category:"engagement",label:"initial_load"})},[e]),e)?(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(s.default,{src:`https://www.googletagmanager.com/gtag/js?id=${e}`,strategy:"afterInteractive"})}):null},54413:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>r});let r=(0,o(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/not-found.tsx","default")},61135:()=>{},81468:(e,t,o)=>{Promise.resolve().then(o.bind(o,54413))},82431:(e,t,o)=>{"use strict";o.d(t,{default:()=>n});var r=o(43210);let n=()=>((0,r.useEffect)(()=>{let e="https://greenhacker.dev",t={"@context":"https://schema.org","@type":"WebSite",name:"GreenHacker Portfolio",description:"Professional portfolio showcasing full-stack development projects, AI integrations, and modern web technologies.",url:e,author:{"@type":"Person",name:"GreenHacker"},potentialAction:{"@type":"SearchAction",target:{"@type":"EntryPoint",urlTemplate:`${e}/?q={search_term_string}`},"query-input":"required name=search_term_string"}},o={"@context":"https://schema.org","@type":"Organization",name:"GreenHacker Development",description:"Professional software development services specializing in full-stack web applications and AI-powered solutions.",url:e,logo:`${e}/logo.jpg`,founder:{"@type":"Person",name:"GreenHacker"},contactPoint:{"@type":"ContactPoint",email:"<EMAIL>",contactType:"Professional Inquiries"}},r=(e,t)=>{let o=document.getElementById(e);o&&o.remove();let r=document.createElement("script");r.id=e,r.type="application/ld+json",r.textContent=JSON.stringify(t),document.head.appendChild(r)};return r("person-schema",{"@context":"https://schema.org","@type":"Person",name:"GreenHacker",jobTitle:"Full-Stack Developer & AI Specialist",description:"Experienced full-stack developer specializing in modern web technologies, AI integration, and innovative software solutions. Proficient in React, Next.js, TypeScript, Python, and machine learning.",url:e,sameAs:["https://github.com/GreenHacker420","https://linkedin.com/in/harsh-hirawat-b657061b7","https://codeforces.com/profile/GreenHacker","https://leetcode.com/u/greenhacker420/"],knowsAbout:["JavaScript","TypeScript","React","Next.js","Python","Machine Learning","Artificial Intelligence","Full-Stack Development","Web Development","Software Engineering","Node.js","Three.js","GSAP","Tailwind CSS"],alumniOf:{"@type":"EducationalOrganization",name:"Computer Science Education"}}),r("website-schema",t),r("organization-schema",o),()=>{["person-schema","website-schema","organization-schema"].forEach(e=>{let t=document.getElementById(e);t&&t.remove()})}},[]),null)},85796:(e,t,o)=>{Promise.resolve().then(o.bind(o,94416)),Promise.resolve().then(o.bind(o,46740)),Promise.resolve().then(o.bind(o,88662)),Promise.resolve().then(o.bind(o,82431))},88662:(e,t,o)=>{"use strict";o.d(t,{default:()=>n});var r=o(43210);o(46740);let n=()=>((0,r.useEffect)(()=>{},[]),null)},92450:(e,t,o)=>{"use strict";o.d(t,{default:()=>n});var r=o(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call portfolioEvents() from the server but portfolioEvents is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/analytics/GoogleAnalytics.tsx","portfolioEvents"),(0,r.registerClientReference)(function(){throw Error("Attempted to call trackEvent() from the server but trackEvent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/analytics/GoogleAnalytics.tsx","trackEvent"),(0,r.registerClientReference)(function(){throw Error("Attempted to call trackPageView() from the server but trackPageView is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/analytics/GoogleAnalytics.tsx","trackPageView"),(0,r.registerClientReference)(function(){throw Error("Attempted to call trackEngagement() from the server but trackEngagement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/analytics/GoogleAnalytics.tsx","trackEngagement"),(0,r.registerClientReference)(function(){throw Error("Attempted to call trackConversion() from the server but trackConversion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/analytics/GoogleAnalytics.tsx","trackConversion");let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/analytics/GoogleAnalytics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/analytics/GoogleAnalytics.tsx","default")},94416:(e,t,o)=>{"use strict";o.d(t,{Providers:()=>G});var r=o(60687),n=o(92314),s=o(8693),a=o(43210),i=o(20592),l=o(4780);let c=i.Kq;i.bL,i.l9,a.forwardRef(({className:e,sideOffset:t=4,...o},n)=>(0,r.jsx)(i.UC,{ref:n,sideOffset:t,className:(0,l.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...o})).displayName=i.UC.displayName;let d=(0,a.createContext)(void 0),p=({children:e})=>{let[t,o]=(0,a.useState)("dark");return(0,a.useEffect)(()=>{},[]),(0,r.jsx)(d.Provider,{value:{theme:t,toggleTheme:()=>{o("dark"===t?"light":"dark")}},children:e})};var m=o(29867),u=o(47313),f=o(24224),h=o(11860);let g=u.Kq,v=a.forwardRef(({className:e,...t},o)=>(0,r.jsx)(u.LM,{ref:o,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));v.displayName=u.LM.displayName;let x=(0,f.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),b=a.forwardRef(({className:e,variant:t,...o},n)=>(0,r.jsx)(u.bL,{ref:n,className:(0,l.cn)(x({variant:t}),e),...o}));b.displayName=u.bL.displayName,a.forwardRef(({className:e,...t},o)=>(0,r.jsx)(u.rc,{ref:o,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=u.rc.displayName;let y=a.forwardRef(({className:e,...t},o)=>(0,r.jsx)(u.bm,{ref:o,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,r.jsx)(h.A,{className:"h-4 w-4"})}));y.displayName=u.bm.displayName;let w=a.forwardRef(({className:e,...t},o)=>(0,r.jsx)(u.hE,{ref:o,className:(0,l.cn)("text-sm font-semibold",e),...t}));w.displayName=u.hE.displayName;let j=a.forwardRef(({className:e,...t},o)=>(0,r.jsx)(u.VY,{ref:o,className:(0,l.cn)("text-sm opacity-90",e),...t}));function k(){let{toasts:e}=(0,m.dj)();return(0,r.jsxs)(g,{children:[e.map(function({id:e,title:t,description:o,action:n,...s}){return(0,r.jsxs)(b,{...s,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[t&&(0,r.jsx)(w,{children:t}),o&&(0,r.jsx)(j,{children:o})]}),n,(0,r.jsx)(y,{})]},e)}),(0,r.jsx)(v,{})]})}j.displayName=u.VY.displayName;var N=o(10218),P=o(52581);let _=({...e})=>{let{theme:t="system"}=(0,N.D)();return(0,r.jsx)(P.l$,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})};var E=o(72244);let C=()=>{let[e,t]=(0,a.useState)(0),[o,n]=(0,a.useState)("Initializing system..."),[s,i]=(0,a.useState)(!0),[l,c]=(0,a.useState)(!1),d=[{text:"Initializing system...",duration:1200},{text:"Establishing secure connection...",duration:1e3},{text:"Authenticating credentials...",duration:800},{text:"Bypassing security protocols...",duration:1500},{text:"Loading developer assets...",duration:1e3},{text:"Compiling portfolio data...",duration:1200},{text:"Optimizing display modules...",duration:900},{text:"Rendering interface...",duration:1300},{text:"System ready. Welcome to GreenHacker portfolio v2.0",duration:1e3}];return(0,a.useEffect)(()=>{let e=setInterval(()=>{i(e=>!e)},500),o=0,r=setTimeout(function e(){if(o<d.length){let{text:r,duration:s}=d[o];n(r),t(Math.min(100,Math.round((o+1)/d.length*100))),o++,setTimeout(e,s)}else c(!0),setTimeout(()=>{},1e3)},500);return()=>{clearInterval(e),clearTimeout(r)}},[]),(0,r.jsxs)(E.P.div,{className:"fixed inset-0 bg-black flex items-center justify-center z-50",initial:{opacity:1},exit:{opacity:0},transition:{duration:.6,ease:"easeInOut"},children:[(0,r.jsxs)(E.P.div,{className:"w-full max-w-3xl bg-black border border-neon-green p-6 rounded-md shadow-neon-green terminal-window",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.8}}},initial:"hidden",animate:"visible",children:[(0,r.jsxs)("div",{className:"terminal-header flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{className:"text-neon-green font-mono text-sm",children:"~/green-hacker/portfolio"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500"}),(0,r.jsx)("div",{className:"w-3 h-3 rounded-full bg-yellow-500"}),(0,r.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"})]})]}),(0,r.jsxs)("div",{className:"terminal-content space-y-2 font-mono text-sm overflow-hidden",children:[(0,r.jsxs)("div",{className:"line",children:[(0,r.jsx)("span",{className:"text-neon-blue",children:"$ "}),(0,r.jsx)("span",{className:"text-white",children:"load portfolio --env=production --secure"})]}),(0,r.jsxs)(E.P.div,{className:"line text-neon-green",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:[o,s?"▋":" "]}),(0,r.jsxs)(E.P.div,{className:"line",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:[(0,r.jsxs)("div",{className:"text-github-text",children:["Progress: ",e,"%"]}),(0,r.jsx)("div",{className:"w-full bg-github-dark rounded-full h-2 mt-1",children:(0,r.jsx)(E.P.div,{className:"h-2 rounded-full bg-neon-green",initial:{width:0},animate:{width:`${e}%`},transition:{duration:.5}})})]}),l&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(E.P.div,{className:"line",initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:[(0,r.jsx)("span",{className:"text-neon-blue",children:"$ "}),(0,r.jsx)("span",{className:"text-white",children:"launch --mode=interactive"})]}),(0,r.jsx)(E.P.div,{className:"line text-neon-purple",initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},children:"Launching portfolio interface..."})]})]}),(0,r.jsx)("div",{className:"ascii-art mt-8 text-neon-green font-mono text-xs whitespace-pre",children:` ██████╗ ██████╗ ███████╗███████╗███╗   ██╗██╗  ██╗ █████╗  ██████╗██╗  ██╗███████╗██████╗
██╔════╝ ██╔══██╗██╔════╝██╔════╝████╗  ██║██║  ██║██╔══██╗██╔════╝██║ ██╔╝██╔════╝██╔══██╗
██║  ███╗██████╔╝█████╗  █████╗  ██╔██╗ ██║███████║███████║██║     █████╔╝ █████╗  ██████╔╝
██║   ██║██╔══██╗██╔══╝  ██╔══╝  ██║╚██╗██║██╔══██║██╔══██║██║     ██╔═██╗ ██╔══╝  ██╔══██╗
╚██████╔╝██║  ██║███████╗███████╗██║ ╚████║██║  ██║██║  ██║╚██████╗██║  ██╗███████╗██║  ██║
 ╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝`}),l&&(0,r.jsxs)(E.P.div,{className:"mt-6 text-center",initial:{opacity:0},animate:{opacity:1},transition:{delay:.8},children:[(0,r.jsx)("span",{className:"text-github-text text-sm",children:"Press "}),(0,r.jsx)("span",{className:"px-2 py-1 bg-github-light rounded text-white text-sm mx-1",children:"ENTER"}),(0,r.jsx)("span",{className:"text-github-text text-sm",children:" to continue"})]})]}),(0,r.jsx)("style",{dangerouslySetInnerHTML:{__html:`
        .terminal-window {
          box-shadow: 0 0 10px rgba(63, 185, 80, 0.3), 0 0 20px rgba(63, 185, 80, 0.2);
        }

        @keyframes scan {
          from { top: 0; }
          to { top: 100%; }
        }

        .terminal-window::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background-color: rgba(63, 185, 80, 0.5);
          animation: scan 3s linear infinite;
        }
      `}})]})};var A=o(30036);let S=(0,A.default)(async()=>{},{loadableGenerated:{modules:["app/providers.tsx -> @/components/effects/AnimatedCursor"]},ssr:!1}),I=(0,A.default)(async()=>{},{loadableGenerated:{modules:["app/providers.tsx -> @/components/effects/ReactiveBackground"]},ssr:!1}),T=(0,A.default)(async()=>{},{loadableGenerated:{modules:["app/providers.tsx -> @/components/sections/Chatbot"]},ssr:!1}),D=new n.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1}}});function G({children:e}){let[t,o]=(0,a.useState)(!0),[n,i]=(0,a.useState)(!1),[l,d]=(0,a.useState)(!1);return l?(0,r.jsx)(p,{children:(0,r.jsx)(s.Ht,{client:D,children:(0,r.jsxs)(c,{children:[(0,r.jsx)(k,{}),(0,r.jsx)(_,{}),t&&(0,r.jsx)(C,{}),(0,r.jsx)(I,{}),!n&&(0,r.jsx)(S,{}),e,(0,r.jsx)(T,{})]})})}):null}},94431:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>p,metadata:()=>d});var r=o(37413),n=o(7339),s=o.n(n);o(61135);var a=o(29519),i=o(26785),l=o(92450),c=o(30980);let d={title:{default:"GREENHACKER | Developer Portfolio",template:"%s | GreenHacker Portfolio"},description:"Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects. Specializing in React, Next.js, TypeScript, Python, and machine learning solutions.",keywords:["developer","portfolio","React","Next.js","TypeScript","AI","machine learning","full-stack developer","web development","software engineer","JavaScript","Python","Three.js","GSAP","Tailwind CSS","Node.js","GitHub","open source"],authors:[{name:"GreenHacker",url:"https://greenhacker.tech"}],creator:"GreenHacker",publisher:"GreenHacker",formatDetection:{email:!1,address:!1,telephone:!1},icons:{icon:[{url:"/logo.jpg",sizes:"32x32",type:"image/jpeg"},{url:"/logo.jpg",sizes:"16x16",type:"image/jpeg"}],shortcut:"/logo.jpg",apple:[{url:"/logo.jpg",sizes:"180x180",type:"image/jpeg"}]},manifest:"/site.webmanifest",openGraph:{type:"website",locale:"en_US",url:"https://greenhacker.tech",title:"GREENHACKER | Developer Portfolio",description:"Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.",siteName:"GreenHacker Portfolio",images:[{url:"/logo.jpg",width:1200,height:630,alt:"GreenHacker Portfolio - Full-Stack Developer"}]},twitter:{card:"summary_large_image",title:"GREENHACKER | Developer Portfolio",description:"Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.",creator:"@greenhacker",images:["/logo.jpg"]},robots:{index:!0,follow:!0,nocache:!1,googleBot:{index:!0,follow:!0,noimageindex:!1,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},alternates:{canonical:"https://greenhacker.tech",types:{"application/rss+xml":"https://greenhacker.tech/feed.xml"}},verification:{google:process.env.GOOGLE_SITE_VERIFICATION||"your-google-verification-code",yandex:process.env.YANDEX_VERIFICATION},category:"technology"};function p({children:e}){return(0,r.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("link",{rel:"manifest",href:"/site.webmanifest"}),(0,r.jsx)("meta",{name:"theme-color",content:"#0d1117"}),(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,r.jsx)("meta",{name:"msapplication-TileColor",content:"#0d1117"}),(0,r.jsx)("meta",{name:"msapplication-config",content:"/browserconfig.xml"}),(0,r.jsx)("link",{rel:"canonical",href:"https://greenhacker.tech"}),(0,r.jsx)("link",{rel:"sitemap",type:"application/xml",href:"/sitemap.xml"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://api.github.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://avatars.githubusercontent.com"}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"//www.googletagmanager.com"}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"//www.google-analytics.com"})]}),(0,r.jsxs)("body",{className:s().className,suppressHydrationWarning:!0,children:[(0,r.jsx)(l.default,{}),(0,r.jsx)(i.default,{}),(0,r.jsx)(c.default,{}),(0,r.jsx)(a.Providers,{children:e})]})]})}}};