{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/tooltip.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,kBAAkB,mKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,mKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/theme/ThemeProvider.tsx"], "sourcesContent": ["\n'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\ntype Theme = 'dark' | 'light';\n\ninterface ThemeContextType {\n  theme: Theme;\n  toggleTheme: () => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  // Default to dark theme for \"Green Hacker\" theme\n  const [theme, setTheme] = useState<Theme>('dark');\n\n  // Initialize theme from localStorage or system preference\n  useEffect(() => {\n    // Check if we're on the client side\n    if (typeof window === 'undefined') return;\n\n    const savedTheme = localStorage.getItem('theme') as Theme | null;\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n\n    if (savedTheme) {\n      setTheme(savedTheme);\n      document.documentElement.classList.toggle('dark', savedTheme === 'dark');\n    } else if (prefersDark) {\n      setTheme('dark');\n      document.documentElement.classList.add('dark');\n    }\n  }, []);\n\n  // Update HTML class and localStorage when theme changes\n  const toggleTheme = () => {\n    const newTheme = theme === 'dark' ? 'light' : 'dark';\n    setTheme(newTheme);\n\n    // Check if we're on the client side\n    if (typeof window !== 'undefined') {\n      document.documentElement.classList.toggle('dark', newTheme === 'dark');\n      localStorage.setItem('theme', newTheme);\n    }\n  };\n\n  return (\n    <ThemeContext.Provider value={{ theme, toggleTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\nexport const useTheme = (): ThemeContextType => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAGA;AAFA;;;AAWA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,gBAAyD,CAAC,EAAE,QAAQ,EAAE;IACjF,iDAAiD;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAE1C,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oCAAoC;QACpC,wCAAmC;;QAEnC,MAAM;QACN,MAAM;IASR,GAAG,EAAE;IAEL,wDAAwD;IACxD,MAAM,cAAc;QAClB,MAAM,WAAW,UAAU,SAAS,UAAU;QAC9C,SAAS;QAET,oCAAoC;QACpC,uCAAmC;;QAGnC;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;QAAY;kBAChD;;;;;;AAGP;AAEO,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/hooks/use-toast.ts"], "sourcesContent": ["import * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"@/components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n"], "names": [], "mappings": ";;;;;AAAA;;AAOA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,UAAU,IAAI,CAAC;QACf,OAAO;YACL,MAAM,QAAQ,UAAU,OAAO,CAAC;YAChC,IAAI,QAAQ,CAAC,GAAG;gBACd,UAAU,MAAM,CAAC,OAAO;YAC1B;QACF;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/toast.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\n  {\n    variants: {\n      variant: {\n        default: \"border bg-background text-foreground\",\n        destructive:\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\n    VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  )\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn(\"text-sm opacity-90\", className)}\n    {...props}\n  />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AAEA;;;;;;;AAEA,MAAM,gBAAgB,iKAAA,CAAA,WAAwB;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,iKAAA,CAAA,WAAwB,CAAC,WAAW;AAEhE,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6lBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,qBACE,8OAAC,iKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AACA,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAoB,CAAC,WAAW;AAEpD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,SAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sgBACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,iKAAA,CAAA,SAAsB,CAAC,WAAW;AAE5D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yVACA;QAEF,eAAY;QACX,GAAG,KAAK;kBAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,iKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,iKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,iKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/toaster.tsx"], "sourcesContent": ["import { useToast } from \"@/hooks/use-toast\"\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from \"@/components/ui/toast\"\n\nexport function Toaster() {\n  const { toasts } = useToast()\n\n  return (\n    <ToastProvider>\n      {toasts.map(function ({ id, title, description, action, ...props }) {\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {title && <ToastTitle>{title}</ToastTitle>}\n              {description && (\n                <ToastDescription>{description}</ToastDescription>\n              )}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        )\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAE1B,qBACE,8OAAC,iIAAA,CAAA,gBAAa;;YACX,OAAO,GAAG,CAAC,SAAU,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,OAAO;gBAChE,qBACE,8OAAC,iIAAA,CAAA,QAAK;oBAAW,GAAG,KAAK;;sCACvB,8OAAC;4BAAI,WAAU;;gCACZ,uBAAS,8OAAC,iIAAA,CAAA,aAAU;8CAAE;;;;;;gCACtB,6BACC,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAGtB;sCACD,8OAAC,iIAAA,CAAA,aAAU;;;;;;mBARD;;;;;YAWhB;0BACA,8OAAC,iIAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/sonner.tsx"], "sourcesContent": ["import { useTheme } from \"next-themes\"\nimport { Toaster as Sonner, toast } from \"sonner\"\n\ntype ToasterProps = React.ComponentProps<typeof Sonner>\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      toastOptions={{\n        classNames: {\n          toast:\n            \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\n          description: \"group-[.toast]:text-muted-foreground\",\n          actionButton:\n            \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\n          cancelButton:\n            \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\",\n        },\n      }}\n      {...props}\n    />\n  )\n}\n\nexport { Toaster, toast }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAIA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC,wIAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,cAAc;YACZ,YAAY;gBACV,OACE;gBACF,aAAa;gBACb,cACE;gBACF,cACE;YACJ;QACF;QACC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/LoadingScreen.tsx"], "sourcesContent": ["\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\n\nconst LoadingScreen = () => {\n  const [loadingProgress, setLoadingProgress] = useState(0);\n  const [loadingText, setLoadingText] = useState('Initializing system...');\n  const [showCursor, setShowCursor] = useState(true);\n  const [commandComplete, setCommandComplete] = useState(false);\n\n  const loadingSteps = [\n    { text: 'Initializing system...', duration: 1200 },\n    { text: 'Establishing secure connection...', duration: 1000 },\n    { text: 'Authenticating credentials...', duration: 800 },\n    { text: 'Bypassing security protocols...', duration: 1500 },\n    { text: 'Loading developer assets...', duration: 1000 },\n    { text: 'Compiling portfolio data...', duration: 1200 },\n    { text: 'Optimizing display modules...', duration: 900 },\n    { text: 'Rendering interface...', duration: 1300 },\n    { text: 'System ready. Welcome to GreenHacker portfolio v2.0', duration: 1000 }\n  ];\n\n  useEffect(() => {\n    // Cursor blinking effect\n    const cursorInterval = setInterval(() => {\n      setShowCursor((prev) => !prev);\n    }, 500);\n\n    // Loading progress simulation\n    let step = 0;\n    const progressInterval = setTimeout(function runStep() {\n      if (step < loadingSteps.length) {\n        const { text, duration } = loadingSteps[step];\n        setLoadingText(text);\n        setLoadingProgress(Math.min(100, Math.round((step + 1) / loadingSteps.length * 100)));\n\n        step++;\n        setTimeout(runStep, duration);\n      } else {\n        setCommandComplete(true);\n        setTimeout(() => {\n          if (typeof window !== 'undefined') {\n            const event = new Event('loadingComplete');\n            window.dispatchEvent(event);\n          }\n        }, 1000);\n      }\n    }, 500);\n\n    return () => {\n      clearInterval(cursorInterval);\n      clearTimeout(progressInterval);\n    };\n  }, []);\n\n  const terminalVariants = {\n    hidden: { opacity: 0 },\n    visible: { opacity: 1, transition: { duration: 0.8 } }\n  };\n\n  return (\n    <motion.div\n      className=\"fixed inset-0 bg-black flex items-center justify-center z-50\"\n      initial={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      transition={{ duration: 0.6, ease: \"easeInOut\" }}\n    >\n      <motion.div\n        className=\"w-full max-w-3xl bg-black border border-neon-green p-6 rounded-md shadow-neon-green terminal-window\"\n        variants={terminalVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <div className=\"terminal-header flex items-center justify-between mb-4\">\n          <div className=\"text-neon-green font-mono text-sm\">~/green-hacker/portfolio</div>\n          <div className=\"flex space-x-2\">\n            <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\n            <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\n            <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n          </div>\n        </div>\n\n        <div className=\"terminal-content space-y-2 font-mono text-sm overflow-hidden\">\n          <div className=\"line\">\n            <span className=\"text-neon-blue\">$ </span>\n            <span className=\"text-white\">load portfolio --env=production --secure</span>\n          </div>\n\n          <motion.div\n            className=\"line text-neon-green\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.3 }}\n          >\n            {loadingText}{showCursor ? '▋' : ' '}\n          </motion.div>\n\n          <motion.div\n            className=\"line\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.5 }}\n          >\n            <div className=\"text-github-text\">Progress: {loadingProgress}%</div>\n            <div className=\"w-full bg-github-dark rounded-full h-2 mt-1\">\n              <motion.div\n                className=\"h-2 rounded-full bg-neon-green\"\n                initial={{ width: 0 }}\n                animate={{ width: `${loadingProgress}%` }}\n                transition={{ duration: 0.5 }}\n              ></motion.div>\n            </div>\n          </motion.div>\n\n          {commandComplete && (\n            <>\n              <motion.div\n                className=\"line\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.2 }}\n              >\n                <span className=\"text-neon-blue\">$ </span>\n                <span className=\"text-white\">launch --mode=interactive</span>\n              </motion.div>\n              <motion.div\n                className=\"line text-neon-purple\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.4 }}\n              >\n                Launching portfolio interface...\n              </motion.div>\n            </>\n          )}\n        </div>\n\n        <div className=\"ascii-art mt-8 text-neon-green font-mono text-xs whitespace-pre\">\n{` ██████╗ ██████╗ ███████╗███████╗███╗   ██╗██╗  ██╗ █████╗  ██████╗██╗  ██╗███████╗██████╗\n██╔════╝ ██╔══██╗██╔════╝██╔════╝████╗  ██║██║  ██║██╔══██╗██╔════╝██║ ██╔╝██╔════╝██╔══██╗\n██║  ███╗██████╔╝█████╗  █████╗  ██╔██╗ ██║███████║███████║██║     █████╔╝ █████╗  ██████╔╝\n██║   ██║██╔══██╗██╔══╝  ██╔══╝  ██║╚██╗██║██╔══██║██╔══██║██║     ██╔═██╗ ██╔══╝  ██╔══██╗\n╚██████╔╝██║  ██║███████╗███████╗██║ ╚████║██║  ██║██║  ██║╚██████╗██║  ██╗███████╗██║  ██║\n ╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝`}\n        </div>\n\n        {commandComplete && (\n          <motion.div\n            className=\"mt-6 text-center\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.8 }}\n          >\n            <span className=\"text-github-text text-sm\">Press </span>\n            <span className=\"px-2 py-1 bg-github-light rounded text-white text-sm mx-1\">ENTER</span>\n            <span className=\"text-github-text text-sm\"> to continue</span>\n          </motion.div>\n        )}\n      </motion.div>\n\n      <style dangerouslySetInnerHTML={{__html: `\n        .terminal-window {\n          box-shadow: 0 0 10px rgba(63, 185, 80, 0.3), 0 0 20px rgba(63, 185, 80, 0.2);\n        }\n\n        @keyframes scan {\n          from { top: 0; }\n          to { top: 100%; }\n        }\n\n        .terminal-window::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          height: 3px;\n          background-color: rgba(63, 185, 80, 0.5);\n          animation: scan 3s linear infinite;\n        }\n      `}} />\n    </motion.div>\n  );\n};\n\nexport default LoadingScreen;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAHA;;;;AAKA,MAAM,gBAAgB;IACpB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,eAAe;QACnB;YAAE,MAAM;YAA0B,UAAU;QAAK;QACjD;YAAE,MAAM;YAAqC,UAAU;QAAK;QAC5D;YAAE,MAAM;YAAiC,UAAU;QAAI;QACvD;YAAE,MAAM;YAAmC,UAAU;QAAK;QAC1D;YAAE,MAAM;YAA+B,UAAU;QAAK;QACtD;YAAE,MAAM;YAA+B,UAAU;QAAK;QACtD;YAAE,MAAM;YAAiC,UAAU;QAAI;QACvD;YAAE,MAAM;YAA0B,UAAU;QAAK;QACjD;YAAE,MAAM;YAAuD,UAAU;QAAK;KAC/E;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yBAAyB;QACzB,MAAM,iBAAiB,YAAY;YACjC,cAAc,CAAC,OAAS,CAAC;QAC3B,GAAG;QAEH,8BAA8B;QAC9B,IAAI,OAAO;QACX,MAAM,mBAAmB,WAAW,SAAS;YAC3C,IAAI,OAAO,aAAa,MAAM,EAAE;gBAC9B,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,YAAY,CAAC,KAAK;gBAC7C,eAAe;gBACf,mBAAmB,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM,GAAG;gBAE/E;gBACA,WAAW,SAAS;YACtB,OAAO;gBACL,mBAAmB;gBACnB,WAAW;oBACT,uCAAmC;;oBAGnC;gBACF,GAAG;YACL;QACF,GAAG;QAEH,OAAO;YACL,cAAc;YACd,aAAa;QACf;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YAAE,SAAS;YAAG,YAAY;gBAAE,UAAU;YAAI;QAAE;IACvD;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,YAAY;YAAE,UAAU;YAAK,MAAM;QAAY;;0BAE/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAQ;;kCAER,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAoC;;;;;;0CACnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;kCAInB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAiB;;;;;;kDACjC,8OAAC;wCAAK,WAAU;kDAAa;;;;;;;;;;;;0CAG/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;gCAAI;;oCAExB;oCAAa,aAAa,MAAM;;;;;;;0CAGnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,8OAAC;wCAAI,WAAU;;4CAAmB;4CAAW;4CAAgB;;;;;;;kDAC7D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO,GAAG,gBAAgB,CAAC,CAAC;4CAAC;4CACxC,YAAY;gDAAE,UAAU;4CAAI;;;;;;;;;;;;;;;;;4BAKjC,iCACC;;kDACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,8OAAC;gDAAK,WAAU;0DAAa;;;;;;;;;;;;kDAE/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,OAAO;wCAAI;kDAC1B;;;;;;;;;;;;;;kCAOP,8OAAC;wBAAI,WAAU;kCACtB,CAAC;;;;;2FAKyF,CAAC;;;;;;oBAGnF,iCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,8OAAC;gCAAK,WAAU;0CAA2B;;;;;;0CAC3C,8OAAC;gCAAK,WAAU;0CAA4D;;;;;;0CAC5E,8OAAC;gCAAK,WAAU;0CAA2B;;;;;;;;;;;;;;;;;;0BAKjD,8OAAC;gBAAM,yBAAyB;oBAAC,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;MAoB1C,CAAC;gBAAA;;;;;;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 937, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/providers.tsx"], "sourcesContent": ["\n'use client';\n\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\nimport { TooltipProvider } from \"@/components/ui/tooltip\";\nimport { ThemeProvider } from \"@/components/theme/ThemeProvider\";\nimport { Toaster } from \"@/components/ui/toaster\";\nimport { Toaster as Sonner } from \"@/components/ui/sonner\";\nimport { useState, useEffect } from \"react\";\nimport LoadingScreen from \"@/components/sections/LoadingScreen\";\nimport dynamic from \"next/dynamic\";\n\n// Dynamically import client-only components\nconst AnimatedCursor = dynamic(() => import(\"@/components/effects/AnimatedCursor\"), {\n  ssr: false\n});\n\nconst ReactiveBackground = dynamic(() => import(\"@/components/effects/ReactiveBackground\"), {\n  ssr: false\n});\n\nconst Chatbot = dynamic(() => import(\"@/components/sections/Chatbot\"), {\n  ssr: false\n});\n\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      staleTime: 60 * 1000, // 1 minute\n      refetchOnWindowFocus: false,\n    },\n  },\n});\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  const [isLoading, setIsLoading] = useState(true);\n  const [isMobile, setIsMobile] = useState(false);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n    \n    // Check if user is on mobile device\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n    \n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    \n    // Check if user has already seen the loading screen\n    const hasLoadingBeenShown = sessionStorage.getItem('loadingShown');\n    \n    if (hasLoadingBeenShown) {\n      setIsLoading(false);\n    } else {\n      // Add event listener for when loading is complete\n      const handleLoadingComplete = () => {\n        setTimeout(() => {\n          setIsLoading(false);\n          sessionStorage.setItem('loadingShown', 'true');\n        }, 1000);\n      };\n      \n      window.addEventListener('loadingComplete', handleLoadingComplete);\n      \n      // Fallback in case loading screen gets stuck\n      const timeout = setTimeout(() => {\n        setIsLoading(false);\n        sessionStorage.setItem('loadingShown', 'true');\n      }, 12000);\n      \n      return () => {\n        window.removeEventListener('loadingComplete', handleLoadingComplete);\n        window.removeEventListener('resize', checkMobile);\n        clearTimeout(timeout);\n      };\n    }\n    \n    return () => {\n      window.removeEventListener('resize', checkMobile);\n    };\n  }, []);\n\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <ThemeProvider>\n      <QueryClientProvider client={queryClient}>\n        <TooltipProvider>\n          <Toaster />\n          <Sonner />\n          \n          {isLoading && <LoadingScreen />}\n          \n          {/* Add reactive background for global effect */}\n          <ReactiveBackground />\n          \n          {/* Only show custom cursor on desktop */}\n          {!isMobile && <AnimatedCursor />}\n          \n          {children}\n          \n          <Chatbot />\n        </TooltipProvider>\n      </QueryClientProvider>\n    </ThemeProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;AATA;;;;;;;;;;AAWA,4CAA4C;AAC5C,MAAM,iBAAiB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAC3B,KAAK;;AAGP,MAAM,qBAAqB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAC/B,KAAK;;AAGP,MAAM,UAAU,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IACpB,KAAK;;AAGP,MAAM,cAAc,IAAI,6KAAA,CAAA,cAAW,CAAC;IAClC,gBAAgB;QACd,SAAS;YACP,WAAW,KAAK;YAChB,sBAAsB;QACxB;IACF;AACF;AAEO,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QAEX,oCAAoC;QACpC,MAAM,cAAc;YAClB,YAAY,OAAO,UAAU,GAAG;QAClC;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAElC,oDAAoD;QACpD,MAAM,sBAAsB,eAAe,OAAO,CAAC;QAEnD,IAAI,qBAAqB;YACvB,aAAa;QACf,OAAO;YACL,kDAAkD;YAClD,MAAM,wBAAwB;gBAC5B,WAAW;oBACT,aAAa;oBACb,eAAe,OAAO,CAAC,gBAAgB;gBACzC,GAAG;YACL;YAEA,OAAO,gBAAgB,CAAC,mBAAmB;YAE3C,6CAA6C;YAC7C,MAAM,UAAU,WAAW;gBACzB,aAAa;gBACb,eAAe,OAAO,CAAC,gBAAgB;YACzC,GAAG;YAEH,OAAO;gBACL,OAAO,mBAAmB,CAAC,mBAAmB;gBAC9C,OAAO,mBAAmB,CAAC,UAAU;gBACrC,aAAa;YACf;QACF;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC,4IAAA,CAAA,gBAAa;kBACZ,cAAA,8OAAC,sLAAA,CAAA,sBAAmB;YAAC,QAAQ;sBAC3B,cAAA,8OAAC,mIAAA,CAAA,kBAAe;;kCACd,8OAAC,mIAAA,CAAA,UAAO;;;;;kCACR,8OAAC,kJAAA,CAAA,UAAM;;;;;oBAEN,2BAAa,8OAAC,+IAAA,CAAA,UAAa;;;;;kCAG5B,8OAAC;;;;;oBAGA,CAAC,0BAAY,8OAAC;;;;;oBAEd;kCAED,8OAAC;;;;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/seo/StructuredData.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\ninterface PersonSchema {\n  \"@context\": \"https://schema.org\";\n  \"@type\": \"Person\";\n  name: string;\n  jobTitle: string;\n  description: string;\n  url: string;\n  sameAs: string[];\n  knowsAbout: string[];\n  alumniOf?: {\n    \"@type\": \"EducationalOrganization\";\n    name: string;\n  };\n  worksFor?: {\n    \"@type\": \"Organization\";\n    name: string;\n  };\n}\n\ninterface WebSiteSchema {\n  \"@context\": \"https://schema.org\";\n  \"@type\": \"WebSite\";\n  name: string;\n  description: string;\n  url: string;\n  author: {\n    \"@type\": \"Person\";\n    name: string;\n  };\n  potentialAction: {\n    \"@type\": \"SearchAction\";\n    target: {\n      \"@type\": \"EntryPoint\";\n      urlTemplate: string;\n    };\n    \"query-input\": string;\n  };\n}\n\ninterface OrganizationSchema {\n  \"@context\": \"https://schema.org\";\n  \"@type\": \"Organization\";\n  name: string;\n  description: string;\n  url: string;\n  logo: string;\n  founder: {\n    \"@type\": \"Person\";\n    name: string;\n  };\n  contactPoint: {\n    \"@type\": \"ContactPoint\";\n    email: string;\n    contactType: string;\n  };\n}\n\nconst StructuredData = () => {\n  useEffect(() => {\n    const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://greenhacker.dev';\n\n    // Person Schema\n    const personSchema: PersonSchema = {\n      \"@context\": \"https://schema.org\",\n      \"@type\": \"Person\",\n      name: \"GreenHacker\",\n      jobTitle: \"Full-Stack Developer & AI Specialist\",\n      description: \"Experienced full-stack developer specializing in modern web technologies, AI integration, and innovative software solutions. Proficient in React, Next.js, TypeScript, Python, and machine learning.\",\n      url: baseUrl,\n      sameAs: [\n        \"https://github.com/GreenHacker420\",\n        \"https://linkedin.com/in/harsh-hirawat-b657061b7\",\n        \"https://codeforces.com/profile/GreenHacker\",\n        \"https://leetcode.com/u/greenhacker420/\"\n      ],\n      knowsAbout: [\n        \"JavaScript\",\n        \"TypeScript\",\n        \"React\",\n        \"Next.js\",\n        \"Python\",\n        \"Machine Learning\",\n        \"Artificial Intelligence\",\n        \"Full-Stack Development\",\n        \"Web Development\",\n        \"Software Engineering\",\n        \"Node.js\",\n        \"Three.js\",\n        \"GSAP\",\n        \"Tailwind CSS\"\n      ],\n      alumniOf: {\n        \"@type\": \"EducationalOrganization\",\n        name: \"Computer Science Education\"\n      }\n    };\n\n    // Website Schema\n    const websiteSchema: WebSiteSchema = {\n      \"@context\": \"https://schema.org\",\n      \"@type\": \"WebSite\",\n      name: \"GreenHacker Portfolio\",\n      description: \"Professional portfolio showcasing full-stack development projects, AI integrations, and modern web technologies.\",\n      url: baseUrl,\n      author: {\n        \"@type\": \"Person\",\n        name: \"GreenHacker\"\n      },\n      potentialAction: {\n        \"@type\": \"SearchAction\",\n        target: {\n          \"@type\": \"EntryPoint\",\n          urlTemplate: `${baseUrl}/?q={search_term_string}`\n        },\n        \"query-input\": \"required name=search_term_string\"\n      }\n    };\n\n    // Organization Schema\n    const organizationSchema: OrganizationSchema = {\n      \"@context\": \"https://schema.org\",\n      \"@type\": \"Organization\",\n      name: \"GreenHacker Development\",\n      description: \"Professional software development services specializing in full-stack web applications and AI-powered solutions.\",\n      url: baseUrl,\n      logo: `${baseUrl}/logo.jpg`,\n      founder: {\n        \"@type\": \"Person\",\n        name: \"GreenHacker\"\n      },\n      contactPoint: {\n        \"@type\": \"ContactPoint\",\n        email: \"<EMAIL>\",\n        contactType: \"Professional Inquiries\"\n      }\n    };\n\n    // Function to add or update structured data\n    const addStructuredData = (id: string, schema: any) => {\n      // Remove existing script if it exists\n      const existingScript = document.getElementById(id);\n      if (existingScript) {\n        existingScript.remove();\n      }\n\n      // Add new script\n      const script = document.createElement('script');\n      script.id = id;\n      script.type = 'application/ld+json';\n      script.textContent = JSON.stringify(schema);\n      document.head.appendChild(script);\n    };\n\n    // Add all structured data\n    addStructuredData('person-schema', personSchema);\n    addStructuredData('website-schema', websiteSchema);\n    addStructuredData('organization-schema', organizationSchema);\n\n    // Cleanup function\n    return () => {\n      ['person-schema', 'website-schema', 'organization-schema'].forEach(id => {\n        const script = document.getElementById(id);\n        if (script) {\n          script.remove();\n        }\n      });\n    };\n  }, []);\n\n  return null; // This component doesn't render anything\n};\n\nexport default StructuredData;\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AA6DA,MAAM,iBAAiB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,6EAAyD;QAEzE,gBAAgB;QAChB,MAAM,eAA6B;YACjC,YAAY;YACZ,SAAS;YACT,MAAM;YACN,UAAU;YACV,aAAa;YACb,KAAK;YACL,QAAQ;gBACN;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,UAAU;gBACR,SAAS;gBACT,MAAM;YACR;QACF;QAEA,iBAAiB;QACjB,MAAM,gBAA+B;YACnC,YAAY;YACZ,SAAS;YACT,MAAM;YACN,aAAa;YACb,KAAK;YACL,QAAQ;gBACN,SAAS;gBACT,MAAM;YACR;YACA,iBAAiB;gBACf,SAAS;gBACT,QAAQ;oBACN,SAAS;oBACT,aAAa,GAAG,QAAQ,wBAAwB,CAAC;gBACnD;gBACA,eAAe;YACjB;QACF;QAEA,sBAAsB;QACtB,MAAM,qBAAyC;YAC7C,YAAY;YACZ,SAAS;YACT,MAAM;YACN,aAAa;YACb,KAAK;YACL,MAAM,GAAG,QAAQ,SAAS,CAAC;YAC3B,SAAS;gBACP,SAAS;gBACT,MAAM;YACR;YACA,cAAc;gBACZ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;QAEA,4CAA4C;QAC5C,MAAM,oBAAoB,CAAC,IAAY;YACrC,sCAAsC;YACtC,MAAM,iBAAiB,SAAS,cAAc,CAAC;YAC/C,IAAI,gBAAgB;gBAClB,eAAe,MAAM;YACvB;YAEA,iBAAiB;YACjB,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,OAAO,EAAE,GAAG;YACZ,OAAO,IAAI,GAAG;YACd,OAAO,WAAW,GAAG,KAAK,SAAS,CAAC;YACpC,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;QAEA,0BAA0B;QAC1B,kBAAkB,iBAAiB;QACnC,kBAAkB,kBAAkB;QACpC,kBAAkB,uBAAuB;QAEzC,mBAAmB;QACnB,OAAO;YACL;gBAAC;gBAAiB;gBAAkB;aAAsB,CAAC,OAAO,CAAC,CAAA;gBACjE,MAAM,SAAS,SAAS,cAAc,CAAC;gBACvC,IAAI,QAAQ;oBACV,OAAO,MAAM;gBACf;YACF;QACF;IACF,GAAG,EAAE;IAEL,OAAO,MAAM,yCAAyC;AACxD;uCAEe", "debugId": null}}, {"offset": {"line": 1222, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/analytics/GoogleAnalytics.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport Script from 'next/script';\n\ninterface GoogleAnalyticsProps {\n  measurementId?: string;\n}\n\n// Custom event types for better type safety\nexport interface GAEvent {\n  action: string;\n  category: string;\n  label?: string;\n  value?: number;\n}\n\n// Predefined events for the portfolio\nexport const portfolioEvents = {\n  // Resume interactions\n  resumeView: {\n    action: 'view_resume',\n    category: 'resume',\n    label: 'pdf_preview'\n  },\n  resumeDownload: {\n    action: 'download_resume',\n    category: 'resume',\n    label: 'pdf_download'\n  },\n  resumeError: {\n    action: 'resume_error',\n    category: 'resume',\n    label: 'pdf_load_failed'\n  },\n  \n  // Contact interactions\n  contactFormSubmit: {\n    action: 'submit_form',\n    category: 'contact',\n    label: 'contact_form'\n  },\n  \n  // Project interactions\n  projectView: {\n    action: 'view_project',\n    category: 'projects',\n    label: 'project_details'\n  },\n  projectLink: {\n    action: 'click_project_link',\n    category: 'projects',\n    label: 'external_link'\n  },\n  \n  // GitHub stats\n  githubStatsView: {\n    action: 'view_github_stats',\n    category: 'github',\n    label: 'stats_display'\n  },\n  \n  // AI Chat interactions\n  aiChatStart: {\n    action: 'start_chat',\n    category: 'ai_chat',\n    label: 'terminal_chat'\n  },\n  aiChatMessage: {\n    action: 'send_message',\n    category: 'ai_chat',\n    label: 'user_message'\n  },\n  \n  // 3D interactions\n  threeDInteraction: {\n    action: 'interact_3d',\n    category: '3d_elements',\n    label: 'canvas_interaction'\n  },\n  \n  // Navigation\n  sectionView: {\n    action: 'view_section',\n    category: 'navigation',\n    label: 'scroll_to_section'\n  }\n};\n\n// Global gtag function declaration\ndeclare global {\n  interface Window {\n    gtag: (\n      command: 'config' | 'event' | 'js',\n      targetId: string | Date,\n      config?: any\n    ) => void;\n  }\n}\n\nconst GoogleAnalytics: React.FC<GoogleAnalyticsProps> = ({ \n  measurementId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID \n}) => {\n  useEffect(() => {\n    if (!measurementId) {\n      console.warn('Google Analytics measurement ID not provided');\n      return;\n    }\n\n    // Initialize GA4 when the script loads\n    window.gtag = window.gtag || function() {\n      (window.gtag as any).q = (window.gtag as any).q || [];\n      (window.gtag as any).q.push(arguments);\n    };\n\n    window.gtag('js', new Date());\n    window.gtag('config', measurementId, {\n      page_title: 'GreenHacker Portfolio',\n      page_location: window.location.href,\n      send_page_view: true,\n      // Enhanced measurement for better insights\n      enhanced_measurement: {\n        scrolls: true,\n        outbound_clicks: true,\n        site_search: true,\n        video_engagement: true,\n        file_downloads: true\n      },\n      // Custom parameters for portfolio tracking\n      custom_map: {\n        'custom_parameter_1': 'section_name',\n        'custom_parameter_2': 'interaction_type'\n      }\n    });\n\n    // Track initial page view\n    trackEvent({\n      action: 'page_view',\n      category: 'engagement',\n      label: 'initial_load'\n    });\n\n  }, [measurementId]);\n\n  if (!measurementId) {\n    return null;\n  }\n\n  return (\n    <>\n      <Script\n        src={`https://www.googletagmanager.com/gtag/js?id=${measurementId}`}\n        strategy=\"afterInteractive\"\n      />\n    </>\n  );\n};\n\n// Utility function to track custom events\nexport const trackEvent = (event: GAEvent) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', event.action, {\n      event_category: event.category,\n      event_label: event.label,\n      value: event.value,\n      // Add timestamp for better analytics\n      custom_parameter_timestamp: new Date().toISOString()\n    });\n  }\n};\n\n// Utility function to track page views\nexport const trackPageView = (url: string, title?: string) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || '', {\n      page_location: url,\n      page_title: title || document.title\n    });\n  }\n};\n\n// Utility function to track user engagement\nexport const trackEngagement = (engagementTime: number) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', 'user_engagement', {\n      engagement_time_msec: engagementTime\n    });\n  }\n};\n\n// Utility function to track conversions (e.g., contact form submissions)\nexport const trackConversion = (conversionName: string, value?: number) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', 'conversion', {\n      send_to: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,\n      event_category: 'conversion',\n      event_label: conversionName,\n      value: value\n    });\n  }\n};\n\nexport default GoogleAnalytics;\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAkBO,MAAM,kBAAkB;IAC7B,sBAAsB;IACtB,YAAY;QACV,QAAQ;QACR,UAAU;QACV,OAAO;IACT;IACA,gBAAgB;QACd,QAAQ;QACR,UAAU;QACV,OAAO;IACT;IACA,aAAa;QACX,QAAQ;QACR,UAAU;QACV,OAAO;IACT;IAEA,uBAAuB;IACvB,mBAAmB;QACjB,QAAQ;QACR,UAAU;QACV,OAAO;IACT;IAEA,uBAAuB;IACvB,aAAa;QACX,QAAQ;QACR,UAAU;QACV,OAAO;IACT;IACA,aAAa;QACX,QAAQ;QACR,UAAU;QACV,OAAO;IACT;IAEA,eAAe;IACf,iBAAiB;QACf,QAAQ;QACR,UAAU;QACV,OAAO;IACT;IAEA,uBAAuB;IACvB,aAAa;QACX,QAAQ;QACR,UAAU;QACV,OAAO;IACT;IACA,eAAe;QACb,QAAQ;QACR,UAAU;QACV,OAAO;IACT;IAEA,kBAAkB;IAClB,mBAAmB;QACjB,QAAQ;QACR,UAAU;QACV,OAAO;IACT;IAEA,aAAa;IACb,aAAa;QACX,QAAQ;QACR,UAAU;QACV,OAAO;IACT;AACF;AAaA,MAAM,kBAAkD,CAAC,EACvD,gBAAgB,QAAQ,GAAG,CAAC,6BAA6B,EAC1D;IACC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe;YAClB,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,uCAAuC;QACvC,OAAO,IAAI,GAAG,OAAO,IAAI,IAAI;YAC1B,OAAO,IAAI,CAAS,CAAC,GAAG,AAAC,OAAO,IAAI,CAAS,CAAC,IAAI,EAAE;YACpD,OAAO,IAAI,CAAS,CAAC,CAAC,IAAI,CAAC;QAC9B;QAEA,OAAO,IAAI,CAAC,MAAM,IAAI;QACtB,OAAO,IAAI,CAAC,UAAU,eAAe;YACnC,YAAY;YACZ,eAAe,OAAO,QAAQ,CAAC,IAAI;YACnC,gBAAgB;YAChB,2CAA2C;YAC3C,sBAAsB;gBACpB,SAAS;gBACT,iBAAiB;gBACjB,aAAa;gBACb,kBAAkB;gBAClB,gBAAgB;YAClB;YACA,2CAA2C;YAC3C,YAAY;gBACV,sBAAsB;gBACtB,sBAAsB;YACxB;QACF;QAEA,0BAA0B;QAC1B,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;QACT;IAEF,GAAG;QAAC;KAAc;IAElB,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IAEA,qBACE;kBACE,cAAA,8OAAC,8HAAA,CAAA,UAAM;YACL,KAAK,CAAC,4CAA4C,EAAE,eAAe;YACnE,UAAS;;;;;;;AAIjB;AAGO,MAAM,aAAa,CAAC;IACzB,uCAAkD;;IAQlD;AACF;AAGO,MAAM,gBAAgB,CAAC,KAAa;IACzC,uCAAkD;;IAKlD;AACF;AAGO,MAAM,kBAAkB,CAAC;IAC9B,uCAAkD;;IAIlD;AACF;AAGO,MAAM,kBAAkB,CAAC,gBAAwB;IACtD,uCAAkD;;IAOlD;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1381, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/performance/WebVitals.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { trackEvent } from '@/components/analytics/GoogleAnalytics';\n\ninterface WebVitalsMetric {\n  id: string;\n  name: string;\n  value: number;\n  delta: number;\n  rating: 'good' | 'needs-improvement' | 'poor';\n}\n\nconst WebVitals = () => {\n  useEffect(() => {\n    // Only run in production and if web vitals API is available\n    if (process.env.NODE_ENV !== 'production' || typeof window === 'undefined') {\n      return;\n    }\n\n    // Function to send metrics to analytics\n    const sendToAnalytics = (metric: WebVitalsMetric) => {\n      // Send to Google Analytics\n      trackEvent({\n        action: 'web_vitals',\n        category: 'performance',\n        label: metric.name,\n        value: Math.round(metric.value)\n      });\n\n      // Also send detailed metric data\n      if (window.gtag) {\n        window.gtag('event', metric.name, {\n          event_category: 'Web Vitals',\n          event_label: metric.id,\n          value: Math.round(metric.value),\n          custom_parameter_rating: metric.rating,\n          custom_parameter_delta: Math.round(metric.delta),\n          non_interaction: true\n        });\n      }\n\n      // Log to console in development\n      if (process.env.NODE_ENV === 'development') {\n        console.log('Web Vitals:', metric);\n      }\n    };\n\n    // Dynamically import web-vitals library\n    import('web-vitals').then((webVitals) => {\n      // Cumulative Layout Shift\n      if (webVitals.onCLS) {\n        webVitals.onCLS(sendToAnalytics);\n      }\n\n      // First Contentful Paint\n      if (webVitals.onFCP) {\n        webVitals.onFCP(sendToAnalytics);\n      }\n\n      // Largest Contentful Paint\n      if (webVitals.onLCP) {\n        webVitals.onLCP(sendToAnalytics);\n      }\n\n      // Time to First Byte\n      if (webVitals.onTTFB) {\n        webVitals.onTTFB(sendToAnalytics);\n      }\n\n      // Interaction to Next Paint (replaces FID in web-vitals v5)\n      if (webVitals.onINP) {\n        webVitals.onINP(sendToAnalytics);\n      }\n    }).catch((error) => {\n      console.warn('Failed to load web-vitals:', error);\n    });\n\n    // Additional performance monitoring\n    const observer = new PerformanceObserver((list) => {\n      for (const entry of list.getEntries()) {\n        // Track long tasks (> 50ms)\n        if (entry.entryType === 'longtask') {\n          trackEvent({\n            action: 'long_task',\n            category: 'performance',\n            label: 'main_thread_blocking',\n            value: Math.round(entry.duration)\n          });\n        }\n\n        // Track navigation timing\n        if (entry.entryType === 'navigation') {\n          const navEntry = entry as PerformanceNavigationTiming;\n\n          // DOM Content Loaded\n          const domContentLoaded = navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart;\n          trackEvent({\n            action: 'dom_content_loaded',\n            category: 'performance',\n            label: 'page_load',\n            value: Math.round(domContentLoaded)\n          });\n\n          // Page Load Complete\n          const loadComplete = navEntry.loadEventEnd - navEntry.loadEventStart;\n          trackEvent({\n            action: 'load_complete',\n            category: 'performance',\n            label: 'page_load',\n            value: Math.round(loadComplete)\n          });\n        }\n\n        // Track resource loading\n        if (entry.entryType === 'resource') {\n          const resourceEntry = entry as PerformanceResourceTiming;\n\n          // Track slow resources (> 1s)\n          if (resourceEntry.duration > 1000) {\n            trackEvent({\n              action: 'slow_resource',\n              category: 'performance',\n              label: resourceEntry.initiatorType || 'unknown',\n              value: Math.round(resourceEntry.duration)\n            });\n          }\n        }\n      }\n    });\n\n    // Observe performance entries\n    try {\n      observer.observe({ entryTypes: ['longtask', 'navigation', 'resource'] });\n    } catch (error) {\n      console.warn('Performance observer not supported:', error);\n    }\n\n    // Track page visibility changes\n    const handleVisibilityChange = () => {\n      if (document.visibilityState === 'hidden') {\n        trackEvent({\n          action: 'page_hidden',\n          category: 'engagement',\n          label: 'visibility_change'\n        });\n      } else {\n        trackEvent({\n          action: 'page_visible',\n          category: 'engagement',\n          label: 'visibility_change'\n        });\n      }\n    };\n\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n\n    // Track user engagement time\n    let startTime = Date.now();\n    let isActive = true;\n\n    const trackEngagementTime = () => {\n      if (isActive) {\n        const engagementTime = Date.now() - startTime;\n        if (engagementTime > 10000) { // Only track if user was engaged for > 10s\n          trackEvent({\n            action: 'engagement_time',\n            category: 'engagement',\n            label: 'session_duration',\n            value: Math.round(engagementTime / 1000) // Convert to seconds\n          });\n        }\n      }\n    };\n\n    // Track when user becomes inactive\n    const handleUserInactive = () => {\n      isActive = false;\n      trackEngagementTime();\n    };\n\n    // Track when user becomes active again\n    const handleUserActive = () => {\n      if (!isActive) {\n        isActive = true;\n        startTime = Date.now();\n      }\n    };\n\n    // Listen for user activity\n    ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {\n      document.addEventListener(event, handleUserActive, { passive: true });\n    });\n\n    // Track when user leaves the page\n    window.addEventListener('beforeunload', handleUserInactive);\n\n    // Cleanup\n    return () => {\n      observer.disconnect();\n      document.removeEventListener('visibilitychange', handleVisibilityChange);\n      window.removeEventListener('beforeunload', handleUserInactive);\n      ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {\n        document.removeEventListener(event, handleUserActive);\n      });\n    };\n  }, []);\n\n  return null; // This component doesn't render anything\n};\n\nexport default WebVitals;\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAaA,MAAM,YAAY;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4DAA4D;QAC5D,wCAA4E;YAC1E;QACF;;QAEA,wCAAwC;QACxC,MAAM;QAyDN,oCAAoC;QACpC,MAAM;QA2DN,gCAAgC;QAChC,MAAM;QAkBN,6BAA6B;QAC7B,IAAI;QACJ,IAAI;QAEJ,MAAM;QAcN,mCAAmC;QACnC,MAAM;QAKN,uCAAuC;QACvC,MAAM;IAwBR,GAAG,EAAE;IAEL,OAAO,MAAM,yCAAyC;AACxD;uCAEe", "debugId": null}}]}