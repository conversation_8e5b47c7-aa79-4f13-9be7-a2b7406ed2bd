{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/sitemap.xml/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\n\nexport async function GET() {\n  const baseUrl = process.env.VERCEL_URL \n    ? `https://${process.env.VERCEL_URL}` \n    : process.env.NODE_ENV === 'production'\n    ? 'https://greenhacker.dev'\n    : 'http://localhost:3000';\n\n  const sitemap = `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\"\n        xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n        xsi:schemaLocation=\"http://www.sitemaps.org/schemas/sitemap/0.9\n        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd\">\n  \n  <!-- Homepage -->\n  <url>\n    <loc>${baseUrl}/</loc>\n    <lastmod>${new Date().toISOString()}</lastmod>\n    <changefreq>weekly</changefreq>\n    <priority>1.0</priority>\n  </url>\n  \n  <!-- Resume PDF -->\n  <url>\n    <loc>${baseUrl}/resume.pdf</loc>\n    <lastmod>${new Date().toISOString()}</lastmod>\n    <changefreq>monthly</changefreq>\n    <priority>0.8</priority>\n  </url>\n  \n  <!-- API Routes (for search engines that index APIs) -->\n  <url>\n    <loc>${baseUrl}/api/github/stats</loc>\n    <lastmod>${new Date().toISOString()}</lastmod>\n    <changefreq>daily</changefreq>\n    <priority>0.5</priority>\n  </url>\n  \n</urlset>`;\n\n  return new NextResponse(sitemap, {\n    headers: {\n      'Content-Type': 'application/xml',\n      'Cache-Control': 'public, max-age=86400, s-maxage=86400', // Cache for 24 hours\n    },\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe;IACpB,MAAM,UAAU,QAAQ,GAAG,CAAC,UAAU,GAClC,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE,GACnC,6EAEA;IAEJ,MAAM,UAAU,CAAC;;;;;;;;SAQV,EAAE,QAAQ;aACN,EAAE,IAAI,OAAO,WAAW,GAAG;;;;;;;SAO/B,EAAE,QAAQ;aACN,EAAE,IAAI,OAAO,WAAW,GAAG;;;;;;;SAO/B,EAAE,QAAQ;aACN,EAAE,IAAI,OAAO,WAAW,GAAG;;;;;SAK/B,CAAC;IAER,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,SAAS;QAC/B,SAAS;YACP,gBAAgB;YAChB,iBAAiB;QACnB;IACF;AACF", "debugId": null}}]}