module.exports = {

"[project]/.next-internal/server/app/feed.xml/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/feed.xml/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
async function GET() {
    const baseUrl = process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 'http://localhost:3000';
    const rss = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>GreenHacker Portfolio</title>
    <description>Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.</description>
    <link>${baseUrl}</link>
    <atom:link href="${baseUrl}/feed.xml" rel="self" type="application/rss+xml"/>
    <language>en-US</language>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
    <managingEditor><EMAIL> (GreenHacker)</managingEditor>
    <webMaster><EMAIL> (GreenHacker)</webMaster>
    <category>Technology</category>
    <category>Web Development</category>
    <category>Software Engineering</category>
    <category>Artificial Intelligence</category>
    
    <item>
      <title>Portfolio Launch - Full-Stack Developer Showcase</title>
      <description>Explore my comprehensive portfolio featuring modern web technologies, AI integrations, and innovative projects. Built with Next.js, React, TypeScript, and cutting-edge tools.</description>
      <link>${baseUrl}</link>
      <guid isPermaLink="true">${baseUrl}</guid>
      <pubDate>${new Date().toUTCString()}</pubDate>
      <category>Portfolio</category>
      <category>Web Development</category>
    </item>
    
    <item>
      <title>Resume - Professional Experience and Skills</title>
      <description>Download my comprehensive resume showcasing experience in full-stack development, AI/ML projects, and modern web technologies.</description>
      <link>${baseUrl}/resume.pdf</link>
      <guid isPermaLink="true">${baseUrl}/resume.pdf</guid>
      <pubDate>${new Date().toUTCString()}</pubDate>
      <category>Resume</category>
      <category>Professional</category>
    </item>
    
    <item>
      <title>GitHub Statistics - Open Source Contributions</title>
      <description>Real-time GitHub statistics and contributions showcasing active development and open source participation.</description>
      <link>${baseUrl}/#github-stats</link>
      <guid isPermaLink="true">${baseUrl}/#github-stats</guid>
      <pubDate>${new Date().toUTCString()}</pubDate>
      <category>GitHub</category>
      <category>Open Source</category>
    </item>
    
  </channel>
</rss>`;
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](rss, {
        headers: {
            'Content-Type': 'application/rss+xml',
            'Cache-Control': 'public, max-age=86400, s-maxage=86400'
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__4949b580._.js.map