{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/feed.xml/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\n\nexport async function GET() {\n  const baseUrl = process.env.VERCEL_URL \n    ? `https://${process.env.VERCEL_URL}` \n    : process.env.NODE_ENV === 'production'\n    ? 'https://greenhacker.dev'\n    : 'http://localhost:3000';\n\n  const rss = `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<rss version=\"2.0\" xmlns:atom=\"http://www.w3.org/2005/Atom\">\n  <channel>\n    <title>GreenHacker Portfolio</title>\n    <description>Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.</description>\n    <link>${baseUrl}</link>\n    <atom:link href=\"${baseUrl}/feed.xml\" rel=\"self\" type=\"application/rss+xml\"/>\n    <language>en-US</language>\n    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>\n    <managingEditor><EMAIL> (GreenHacker)</managingEditor>\n    <webMaster><EMAIL> (GreenHacker)</webMaster>\n    <category>Technology</category>\n    <category>Web Development</category>\n    <category>Software Engineering</category>\n    <category>Artificial Intelligence</category>\n    \n    <item>\n      <title>Portfolio Launch - Full-Stack Developer Showcase</title>\n      <description>Explore my comprehensive portfolio featuring modern web technologies, AI integrations, and innovative projects. Built with Next.js, React, TypeScript, and cutting-edge tools.</description>\n      <link>${baseUrl}</link>\n      <guid isPermaLink=\"true\">${baseUrl}</guid>\n      <pubDate>${new Date().toUTCString()}</pubDate>\n      <category>Portfolio</category>\n      <category>Web Development</category>\n    </item>\n    \n    <item>\n      <title>Resume - Professional Experience and Skills</title>\n      <description>Download my comprehensive resume showcasing experience in full-stack development, AI/ML projects, and modern web technologies.</description>\n      <link>${baseUrl}/resume.pdf</link>\n      <guid isPermaLink=\"true\">${baseUrl}/resume.pdf</guid>\n      <pubDate>${new Date().toUTCString()}</pubDate>\n      <category>Resume</category>\n      <category>Professional</category>\n    </item>\n    \n    <item>\n      <title>GitHub Statistics - Open Source Contributions</title>\n      <description>Real-time GitHub statistics and contributions showcasing active development and open source participation.</description>\n      <link>${baseUrl}/#github-stats</link>\n      <guid isPermaLink=\"true\">${baseUrl}/#github-stats</guid>\n      <pubDate>${new Date().toUTCString()}</pubDate>\n      <category>GitHub</category>\n      <category>Open Source</category>\n    </item>\n    \n  </channel>\n</rss>`;\n\n  return new NextResponse(rss, {\n    headers: {\n      'Content-Type': 'application/rss+xml',\n      'Cache-Control': 'public, max-age=86400, s-maxage=86400', // Cache for 24 hours\n    },\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe;IACpB,MAAM,UAAU,QAAQ,GAAG,CAAC,UAAU,GAClC,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE,GACnC,6EAEA;IAEJ,MAAM,MAAM,CAAC;;;;;UAKL,EAAE,QAAQ;qBACC,EAAE,QAAQ;;mBAEZ,EAAE,IAAI,OAAO,WAAW,GAAG;;;;;;;;;;;YAWlC,EAAE,QAAQ;+BACS,EAAE,QAAQ;eAC1B,EAAE,IAAI,OAAO,WAAW,GAAG;;;;;;;;YAQ9B,EAAE,QAAQ;+BACS,EAAE,QAAQ;eAC1B,EAAE,IAAI,OAAO,WAAW,GAAG;;;;;;;;YAQ9B,EAAE,QAAQ;+BACS,EAAE,QAAQ;eAC1B,EAAE,IAAI,OAAO,WAAW,GAAG;;;;;;MAMpC,CAAC;IAEL,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK;QAC3B,SAAS;YACP,gBAAgB;YAChB,iBAAiB;QACnB;IACF;AACF", "debugId": null}}]}